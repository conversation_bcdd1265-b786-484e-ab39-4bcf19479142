package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class NormalWave1058
   {
      
      public function NormalWave1058()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1058);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(413,66000,150,84,25,25,170,90,new BossSkillData0(100,{
            "hurt":550,
            "hurtCount":5
         },1),0,1);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(107,1067,126,21,20,20,190,80,{
            "rate":100,
            "hurtBei":2,
            "atkPer":50,
            "keepTime":10
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(107,1067,126,21,20,20,190,80,{
            "rate":100,
            "hurtBei":2,
            "atkPer":50,
            "keepTime":10
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(107,1067,126,21,20,20,190,80,{
            "rate":100,
            "hurtBei":2,
            "atkPer":50,
            "keepTime":10
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(107,1067,126,21,20,20,190,80,{
            "rate":100,
            "hurtBei":2,
            "atkPer":50,
            "keepTime":10
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(107,1067,126,21,20,20,190,80,{
            "rate":100,
            "hurtBei":2,
            "atkPer":50,
            "keepTime":10
         })));
         _loc2_.addFu(new BossArgVO(201,18000,465,24,30,30,150,80,new BossSkillData0(80,{
            "hurt":350,
            "hurtCount":5
         },2),1003,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(107,1067,126,21,20,20,190,80,{
            "rate":100,
            "hurtBei":2,
            "atkPer":50,
            "keepTime":10
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(107,1067,126,21,20,20,190,80,{
            "rate":100,
            "hurtBei":2,
            "atkPer":50,
            "keepTime":10
         })));
         _loc2_.addFu(new BossArgVO(201,18000,465,24,30,30,150,80,new BossSkillData0(100,{"hurt":350},2),1011,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(107,1067,126,21,20,20,190,80,{
            "rate":100,
            "hurtBei":2,
            "atkPer":50,
            "keepTime":10
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(107,1067,126,21,20,20,190,80,{
            "rate":100,
            "hurtBei":2,
            "atkPer":50,
            "keepTime":10
         })));
         _loc2_.addFu(new BossArgVO(201,18000,465,24,30,30,150,80,new BossSkillData0(80,{
            "hurt":390,
            "hurtCount":3
         },2),1017,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(107,1067,126,21,20,20,190,80,{
            "rate":100,
            "hurtBei":2,
            "atkPer":50,
            "keepTime":10
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

