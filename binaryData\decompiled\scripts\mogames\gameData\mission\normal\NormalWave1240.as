package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class NormalWave1240
   {
      
      public function NormalWave1240()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1240);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(625,350000,2590,550,80,80,300,90,new BossSkillData0(150,{
            "hurt":3800,
            "hurtCount":5
         },3),0,1);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(102,7100,910,100,50,50,200,120,{
            "rate":150,
            "hurtBei":1.6,
            "atkPer":50,
            "keepTime":5
         })));
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(101,7100,910,100,50,50,200,120,{
            "atkPer":60,
            "keepTime":10
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(102,7100,910,100,50,50,200,120,{
            "rate":150,
            "hurtBei":1.6,
            "atkPer":50,
            "keepTime":5
         })));
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(101,7100,910,100,50,50,200,120,{
            "atkPer":60,
            "keepTime":10
         })));
         _loc2_.addFu(new BossArgVO(209,81000,2800,400,50,80,150,80,new BossSkillData1(16,{
            "hurt":2300,
            "roleNum":20
         },2),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(102,7100,910,100,50,50,200,120,{
            "rate":150,
            "hurtBei":1.6,
            "atkPer":50,
            "keepTime":5
         })));
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(101,7100,910,100,50,50,200,120,{
            "atkPer":60,
            "keepTime":10
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(102,7100,910,100,50,50,200,120,{
            "rate":150,
            "hurtBei":1.6,
            "atkPer":50,
            "keepTime":5
         })));
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(101,7100,910,100,50,50,200,120,{
            "atkPer":60,
            "keepTime":10
         })));
         _loc2_.addFu(new BossArgVO(210,81000,2800,400,50,80,150,80,new BossSkillData0(150,{
            "hurt":2360,
            "roleNum":4
         },2),1016,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(102,7100,910,100,50,50,200,120,{
            "rate":150,
            "hurtBei":1.6,
            "atkPer":50,
            "keepTime":5
         })));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(101,7100,910,100,50,50,200,120,{
            "atkPer":60,
            "keepTime":10
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(12,new RoleArgVO(102,7100,910,100,50,50,200,120,{
            "rate":150,
            "hurtBei":1.6,
            "atkPer":50,
            "keepTime":5
         })));
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(101,7100,910,100,50,50,200,120,{
            "atkPer":60,
            "keepTime":10
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(102,7100,910,100,50,50,200,120,{
            "rate":150,
            "hurtBei":1.6,
            "atkPer":50,
            "keepTime":5
         })));
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(101,7100,910,100,50,50,200,120,{
            "atkPer":60,
            "keepTime":10
         })));
         _loc2_.addFu(new BossArgVO(211,81000,2800,400,50,80,150,80,new BossSkillData1(16,{
            "hurt":2260,
            "roleNum":4
         },2),1016,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(102,7100,910,100,50,50,200,120,{
            "rate":150,
            "hurtBei":1.6,
            "atkPer":50,
            "keepTime":5
         })));
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(101,7100,910,100,50,50,200,120,{
            "atkPer":60,
            "keepTime":10
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(102,7100,910,100,50,50,200,120,{
            "rate":150,
            "hurtBei":1.6,
            "atkPer":50,
            "keepTime":5
         })));
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(101,7100,910,100,50,50,200,120,{
            "atkPer":60,
            "keepTime":10
         })));
         _loc2_.addFu(new BossArgVO(212,81000,2800,400,50,80,150,80,new BossSkillData0(150,{
            "hurt":2200,
            "roleNum":20
         },2),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(102,7100,910,100,50,50,200,120,{
            "rate":150,
            "hurtBei":1.6,
            "atkPer":50,
            "keepTime":5
         })));
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(101,7100,910,100,50,50,200,120,{
            "atkPer":60,
            "keepTime":10
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

