package mogames.gameData.mission.secret
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class SecretWave2324
   {
      
      public function SecretWave2324()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2324);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(261,1500000,8800,1020,50,100,200,120,new BossSkillData0(150,{"hurt":10690},3),1011,0);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(236,50000,5000,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(238,50000,5000,100,50,50,170,90,null)));
         _loc2_.addFu(new BossArgVO(293,400000,5000,200,30,30,150,50,new BossSkillData0(150,{
            "hurt":8550,
            "roleNum":20
         },3),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(236,50000,5000,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(237,50000,5000,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(237,50000,5000,100,50,50,170,90,null)));
         _loc2_.addFu(new BossArgVO(290,400000,5000,200,30,30,150,50,new BossSkillData0(150,{
            "hurt":8550,
            "roleNum":20
         },3),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(232,60000,15000,100,50,50,170,50,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(236,50000,5000,100,50,50,170,90,null)));
         _loc2_.addFu(new BossArgVO(291,400000,5000,200,30,30,150,50,new BossSkillData0(150,{
            "hurt":8550,
            "roleNum":20
         },3),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(232,60000,15000,100,50,50,170,50,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(237,50000,5000,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(238,50000,5000,100,50,50,170,90,null)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(232,60000,15000,100,50,50,170,50,null)));
         _loc2_.addFu(new BossArgVO(292,400000,5000,200,30,30,150,50,new BossSkillData0(150,{
            "hurt":8550,
            "roleNum":20
         },3),1005,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

