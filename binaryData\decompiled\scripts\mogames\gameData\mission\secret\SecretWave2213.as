package mogames.gameData.mission.secret
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class SecretWave2213
   {
      
      public function SecretWave2213()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2213);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(257,310000,4000,1010,50,100,200,120,new BossSkillData0(150,{"hurt":4500},5),1009,0);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(237,10500,1350,100,50,50,180,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(237,10500,1350,100,50,50,180,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addFu(new BossArgVO(290,95000,3900,150,30,30,150,50,new BossSkillData0(100,{"hurt":2500},2),1013,0));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(237,10500,1350,100,50,50,180,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(237,10500,1350,100,50,50,180,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(237,10500,1350,100,50,50,180,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(237,10500,1350,100,50,50,180,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(237,10500,1350,100,50,50,180,90,null)));
         _loc2_.addFu(new BossArgVO(290,95000,3900,150,30,30,150,50,new BossSkillData0(100,{"hurt":2500},2),1013,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(237,10500,1350,100,50,50,180,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(237,10500,1350,100,50,50,180,90,null)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(237,10500,1350,100,50,50,180,55,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(237,10500,1350,100,50,50,180,90,null)));
         _loc2_.addFu(new BossArgVO(290,95000,3900,150,30,30,150,50,new BossSkillData0(100,{"hurt":2500},2),1013,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

