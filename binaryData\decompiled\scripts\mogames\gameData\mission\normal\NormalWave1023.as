package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class NormalWave1023
   {
      
      public function NormalWave1023()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1023);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(364,31000,102,19,25,25,170,90,new BossSkillData0(50,{
            "hurt":160,
            "roleNum":15
         },1),0,1);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(107,355,52,9,30,25,150,60,{
            "rate":80,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(107,355,52,9,30,25,150,60,{
            "rate":80,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(107,355,52,9,30,25,150,60,{
            "rate":80,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(107,355,52,9,30,25,150,60,{
            "rate":80,
            "hurtBei":1.8
         })));
         _loc2_.addFu(new BossArgVO(207,8000,88,15,30,30,150,80,new BossSkillData0(40,{"hurt":100},1),1011,0));
         _loc2_.addFu(new BossArgVO(207,8000,88,15,30,30,150,80,new BossSkillData0(40,{"hurt":100},1),1010,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(107,355,52,9,30,25,150,60,{
            "rate":80,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(107,355,52,9,30,25,150,60,{
            "rate":80,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(107,355,52,9,30,25,150,60,{
            "rate":80,
            "hurtBei":1.8
         })));
         _loc2_.addFu(new BossArgVO(207,9000,88,15,30,30,150,80,new BossSkillData0(60,{"hurt":100},1),1009,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(107,355,52,9,30,25,150,60,{
            "rate":80,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(107,355,52,9,30,25,150,60,{
            "rate":80,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(107,355,52,9,30,25,150,60,{
            "rate":80,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

