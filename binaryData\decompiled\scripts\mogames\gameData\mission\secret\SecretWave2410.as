package mogames.gameData.mission.secret
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class SecretWave2410
   {
      
      public function SecretWave2410()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2410);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(299,3500000,12000,2000,50,100,200,120,new BossSkillData0(150,{
            "hurt":20950,
            "hurtCount":5
         },3),1003,0);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(238,40000,8300,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(238,40000,8300,100,50,50,170,90,null)));
         _loc2_.addFu(new BossArgVO(293,700000,9500,2000,30,30,150,50,new BossSkillData0(150,{"hurt":8500},3),1013,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(237,40000,8300,100,50,50,170,90,null)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(232,100000,70000,100,50,50,170,50,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(236,40000,8300,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(236,40000,8300,100,50,50,170,90,null)));
         _loc2_.addFu(new BossArgVO(290,700000,9500,2000,30,30,150,50,new BossSkillData0(150,{"hurt":8500},3),1013,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(236,40000,8300,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(232,100000,70000,100,50,50,170,50,null)));
         _loc2_.addFu(new BossArgVO(290,700000,9500,2000,30,30,150,50,new BossSkillData0(150,{"hurt":8500},3),1013,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(236,40000,8300,100,50,50,170,90,null)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(232,100000,70000,100,50,50,170,50,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(232,100000,70000,100,50,50,170,50,null)));
         _loc2_.addFu(new BossArgVO(290,700000,9500,2000,30,30,150,50,new BossSkillData0(150,{"hurt":8500},3),1013,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(237,40000,8300,100,50,50,170,90,null)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(232,100000,70000,100,50,50,170,50,null)));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

