package mogames.gameData.mission.secret
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class SecretWave2591
   {
      
      public function SecretWave2591()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2591);
         _loc1_.limitBR = new WaveLimitVO(99999999,0.9,0.9);
         _loc1_.zhuBoss = new BossArgVO(744,150000000,800000,800000,50,100,200,120,new BossSkillData0(150,{"hurt":800000},99),1008,0);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(740,4000000,300000,210000,150,150,270,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(741,4000000,300000,210000,150,150,270,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(787,4000000,300000,210000,150,150,270,90,null)));
         _loc2_.addFu(new BossArgVO(743,16000000,450000,500000,100,200,250,50,new BossSkillData0(100,{
            "hurt":700000,
            "roleNum":20
         },2),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(740,4000000,300000,210000,150,150,270,90,null)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(232,7000000,2000000,500000,150,150,270,50,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(232,7000000,2000000,500000,150,150,270,50,null)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(740,4000000,300000,210000,150,150,270,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(741,4000000,300000,210000,150,150,270,90,null)));
         _loc2_.addFu(new BossArgVO(743,16000000,450000,500000,100,200,250,50,new BossSkillData0(100,{"hurt":700000},2),1013,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(787,150000,100000,100,150,150,270,50,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(741,4000000,300000,210000,150,150,270,90,null)));
         _loc2_.addFu(new BossArgVO(743,16000000,450000,500000,100,200,250,50,new BossSkillData0(100,{"hurt":700000},2),1013,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(740,4000000,300000,210000,150,150,270,90,null)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(232,7000000,2000000,500000,150,150,270,50,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(232,7000000,2000000,500000,150,150,270,50,null)));
         _loc2_.addFu(new BossArgVO(743,16000000,450000,500000,100,200,250,50,new BossSkillData0(100,{
            "hurt":700000,
            "roleNum":20
         },2),1005,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

