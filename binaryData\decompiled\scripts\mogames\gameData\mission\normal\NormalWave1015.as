package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class NormalWave1015
   {
      
      public function NormalWave1015()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1015);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(315,20000,95,0,20,20,160,70,new BossSkillData0(50,{
            "hurt":85,
            "keepTime":3
         },2),0,1);
         _loc2_ = new OneWaveVO(50);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(104,247,41,9,25,30,160,60,{
            "rate":150,
            "curePer":80
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(104,247,41,9,25,30,160,60,{
            "rate":150,
            "curePer":80
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(104,247,41,9,25,30,160,60,{
            "rate":150,
            "curePer":80
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(104,247,41,9,25,30,160,60,{
            "rate":150,
            "curePer":80
         })));
         _loc2_.addFu(new BossArgVO(202,6000,55,14,30,30,150,80,new BossSkillData0(50,{"hurt":110},1),1013,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(104,247,41,9,25,30,160,60,{
            "rate":150,
            "curePer":80
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(104,247,41,9,25,30,160,60,{
            "rate":150,
            "curePer":80
         })));
         _loc2_.addFu(new BossArgVO(203,6000,55,14,30,30,150,80,new BossSkillData0(40,{"hurt":120},1),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(104,247,41,9,25,30,160,60,{
            "rate":150,
            "curePer":80
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(104,247,41,9,25,30,160,60,{
            "rate":150,
            "curePer":80
         })));
         _loc2_.addFu(new BossArgVO(203,7000,55,14,30,30,150,80,new BossSkillData0(40,{"hurt":120},1),1009,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(104,247,41,9,25,30,160,60,{
            "rate":150,
            "curePer":80
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(104,247,41,9,25,30,160,60,{
            "rate":150,
            "curePer":80
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

