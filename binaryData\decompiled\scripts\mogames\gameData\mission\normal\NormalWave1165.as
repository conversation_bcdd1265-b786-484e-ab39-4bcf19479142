package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class NormalWave1165
   {
      
      public function NormalWave1165()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1165);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(536,104000,1160,194,95,36,170,90,new BossSkillData0(100,{"hurt":2800},1),0,1);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(110,2500,380,45,20,20,190,80,{
            "rate":130,
            "hurtBei":1.8
         })));
         _loc2_.addFu(new BossArgVO(212,41000,1300,180,30,30,150,80,new BossSkillData0(100,{
            "hurt":900,
            "hurtCount":5
         },1),1003,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(110,2500,380,45,20,20,190,80,{
            "rate":130,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(12,new RoleArgVO(110,2500,380,45,20,20,190,80,{
            "rate":130,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(110,2500,380,45,20,20,190,80,{
            "rate":130,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(110,2500,380,45,20,20,190,80,{
            "rate":130,
            "hurtBei":1.8
         })));
         _loc2_.addFu(new BossArgVO(212,41000,1300,180,30,30,150,80,new BossSkillData0(80,{
            "hurt":890,
            "hurtCount":4
         },2),1017,0));
         _loc2_.addFu(new BossArgVO(212,25000,880,120,30,30,150,80,new BossSkillData0(80,{"hurt":800},2),1011,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(110,2500,380,45,20,20,190,80,{
            "rate":130,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(110,2500,380,45,20,20,190,80,{
            "rate":130,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(110,2500,380,45,20,20,190,80,{
            "rate":130,
            "hurtBei":1.8
         })));
         _loc2_.addFu(new BossArgVO(212,41000,1300,180,30,30,150,80,new BossSkillData0(100,{"hurt":800},2),1011,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(110,2500,380,45,20,20,190,80,{
            "rate":130,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(30,new RoleArgVO(110,2500,380,45,20,20,190,80,{
            "rate":130,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

