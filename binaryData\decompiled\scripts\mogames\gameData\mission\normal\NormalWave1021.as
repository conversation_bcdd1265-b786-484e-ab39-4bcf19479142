package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class NormalWave1021
   {
      
      public function NormalWave1021()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1021);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(362,29000,98,17,25,25,170,90,new BossSkillData0(50,{
            "hurt":150,
            "hurtCount":5
         },1),0,1);
         _loc2_ = new OneWaveVO(50);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(104,303,53,10,30,25,150,70,{
            "rate":150,
            "curePer":50
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(104,303,53,10,30,25,150,70,{
            "rate":150,
            "curePer":50
         })));
         _loc2_.addFu(new BossArgVO(205,9000,81,11,30,30,150,80,new BossSkillData0(60,{
            "hurt":90,
            "hurtCount":3
         },1),1017,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(104,303,53,10,30,25,150,70,{
            "rate":150,
            "curePer":50
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(104,303,53,10,30,25,150,70,{
            "rate":150,
            "curePer":50
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(104,303,53,10,30,25,150,70,{
            "rate":150,
            "curePer":50
         })));
         _loc2_.addFu(new BossArgVO(205,9000,81,11,30,30,150,80,new BossSkillData0(60,{
            "hurt":90,
            "roleNum":3
         },1),1016,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(104,303,53,10,30,25,150,70,{
            "rate":150,
            "curePer":50
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(104,303,53,10,30,25,150,70,{
            "rate":150,
            "curePer":50
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(104,303,53,10,30,25,150,70,{
            "rate":150,
            "curePer":50
         })));
         _loc2_.addFu(new BossArgVO(205,9000,81,11,30,30,150,80,new BossSkillData0(60,{
            "hurt":83,
            "keepTime":8,
            "hurtCount":2
         },1),1015,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(104,303,53,10,30,25,150,70,{
            "rate":150,
            "curePer":50
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(104,303,53,10,30,25,150,70,{
            "rate":150,
            "curePer":50
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

