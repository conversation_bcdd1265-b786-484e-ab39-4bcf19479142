package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class NormalWave1134
   {
      
      public function NormalWave1134()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1134);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(500,98000,1005,172,25,41,170,90,new BossSkillData0(80,{
            "hurt":1050,
            "hurtCount":5
         },1),0,1);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(102,3000,255,42,25,40,180,80,{
            "rate":150,
            "hurtBei":1.6,
            "atkPer":50,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(102,3000,255,42,25,40,180,80,{
            "rate":150,
            "hurtBei":1.6,
            "atkPer":50,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(45);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(106,3000,255,34,25,40,180,80,{"atkPer":15})));
         _loc2_.addFu(new BossArgVO(202,30000,930,130,30,30,150,80,new BossSkillData0(50,{
            "hurt":800,
            "roleNum":20
         },2),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(13,new RoleArgVO(106,3000,255,34,25,40,180,80,{"atkPer":15})));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(13,new RoleArgVO(111,2300,285,24,25,40,180,80,{
            "rate":150,
            "keepTime":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(45);
         _loc2_.addEnemy(new WaveEnemyVO(25,new RoleArgVO(111,2300,285,24,25,40,180,80,{
            "rate":150,
            "keepTime":2
         })));
         _loc2_.addFu(new BossArgVO(203,30000,930,130,30,30,150,80,new BossSkillData0(80,{
            "hurt":760,
            "roleNum":4
         },2),1016,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(105,3000,255,38,25,40,180,80,{
            "rate":150,
            "curePer":100
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(111,2300,285,24,25,40,180,80,{
            "rate":150,
            "keepTime":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(105,3000,255,38,25,40,180,80,{
            "rate":150,
            "curePer":100
         })));
         _loc2_.addFu(new BossArgVO(202,25000,930,130,30,30,150,80,new BossSkillData0(60,{
            "hurt":700,
            "roleNum":20
         },2),1005,0));
         _loc2_.addFu(new BossArgVO(203,25000,930,130,30,30,150,80,new BossSkillData0(60,{"hurt":510},2),1011,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(25,new RoleArgVO(111,2300,285,24,25,40,180,80,{
            "rate":150,
            "keepTime":2
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

