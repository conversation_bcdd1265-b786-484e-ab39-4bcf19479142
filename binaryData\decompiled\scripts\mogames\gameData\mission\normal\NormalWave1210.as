package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class NormalWave1210
   {
      
      public function NormalWave1210()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1210);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(585,200000,1720,330,80,80,300,90,new BossSkillData0(150,{
            "hurt":3260,
            "roleNum":30
         },2),0,1);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(110,3000,480,65,50,80,200,120,{
            "rate":100,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(110,3000,480,65,50,80,200,120,{
            "rate":100,
            "hurtBei":1.8
         })));
         _loc2_.addFu(new BossArgVO(202,61000,2400,320,50,80,150,80,new BossSkillData0(150,{"hurt":1750},2),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(112,4600,480,65,50,80,200,120,{
            "rate":150,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(109,4600,480,65,50,80,200,120,{
            "rate":100,
            "hurtPer":60,
            "spdPer":70,
            "keepTime":3
         })));
         _loc2_.addFu(new BossArgVO(205,61000,2400,320,50,80,150,80,new BossSkillData0(150,{"hurt":1750},2),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(109,4600,480,65,50,80,200,120,{
            "rate":100,
            "hurtPer":60,
            "spdPer":70,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(111,3000,580,65,50,80,200,120,{
            "rate":160,
            "keepTime":2
         })));
         _loc2_.addFu(new BossArgVO(207,61000,2400,320,50,80,150,80,new BossSkillData0(150,{
            "hurt":1750,
            "hurtCount":5
         },2),1003,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(110,3000,480,65,50,80,200,120,{
            "rate":100,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(109,4600,480,65,50,80,200,120,{
            "rate":100,
            "hurtPer":60,
            "spdPer":70,
            "keepTime":3
         })));
         _loc2_.addFu(new BossArgVO(211,61000,2400,320,50,80,150,80,new BossSkillData0(150,{"hurt":1750},2),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(111,3000,580,65,50,80,200,120,{
            "rate":160,
            "keepTime":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(111,3000,580,65,50,80,200,120,{
            "rate":160,
            "keepTime":2
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

