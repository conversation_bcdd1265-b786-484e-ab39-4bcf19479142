package mogames.gameData.mission.res
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   
   public class ConstResVO
   {
      
      private var _id:Oint = new Oint();
      
      public var skin:String;
      
      public function ConstResVO(param1:int, param2:String)
      {
         super();
         MathUtil.saveINT(this._id,param1);
         this.skin = param2;
      }
      
      public function get id() : int
      {
         return MathUtil.loadINT(this._id);
      }
   }
}

