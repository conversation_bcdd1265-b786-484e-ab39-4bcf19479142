package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class NormalWave1162
   {
      
      public function NormalWave1162()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1162);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(533,103000,1145,188,25,34,170,90,new BossSkillData0(100,{"hurt":2360},1),0,1);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(101,3900,365,45,25,30,160,80,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc2_.addFu(new BossArgVO(204,40000,1200,180,30,30,150,80,new BossSkillData0(100,{
            "hurt":600,
            "roleNum":20
         },2),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(111,2400,410,45,25,30,160,80,{
            "rate":50,
            "keepTime":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(111,2400,410,45,25,30,160,80,{
            "rate":50,
            "keepTime":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(45);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(101,3900,365,45,25,30,160,80,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc2_.addFu(new BossArgVO(204,40000,1200,180,30,30,150,80,new BossSkillData0(100,{
            "hurt":950,
            "roleNum":20
         },2),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(110,3900,365,45,25,30,160,80,{
            "rate":50,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(110,3900,365,45,25,30,160,80,{
            "rate":50,
            "keepTime":20
         })));
         _loc2_.addFu(new BossArgVO(211,40000,1200,180,30,30,150,80,new BossSkillData0(100,{"hurt":1000},2),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(110,3900,365,45,25,30,160,80,{
            "rate":50,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(108,2400,410,45,25,30,160,80,{
            "rate":50,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(25,new RoleArgVO(111,2400,410,45,25,30,160,80,{
            "rate":50,
            "keepTime":2
         })));
         _loc2_.addFu(new BossArgVO(203,40000,1200,180,30,30,150,80,new BossSkillData0(100,{"hurt":1000},1),1013,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(108,2400,410,45,25,30,160,80,{
            "rate":50,
            "keepTime":20
         })));
         _loc2_.addFu(new BossArgVO(211,40000,1200,180,30,30,150,80,new BossSkillData0(100,{"hurt":1000},1),1008,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

