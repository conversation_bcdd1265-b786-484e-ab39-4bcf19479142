<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三国存档编辑器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .header h1 {
            color: #333;
            text-align: center;
            margin-bottom: 15px;
        }
        
        .toolbar {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background: #4CAF50;
            color: white;
        }
        
        .btn-secondary {
            background: #2196F3;
            color: white;
        }
        
        .btn-warning {
            background: #FF9800;
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        .main-content {
            display: flex;
            gap: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .sidebar {
            width: 250px;
            background: #f5f5f5;
            padding: 20px;
        }
        
        .sidebar-item {
            padding: 12px 15px;
            margin-bottom: 5px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
            border-left: 4px solid transparent;
        }
        
        .sidebar-item:hover {
            background: #e0e0e0;
            border-left-color: #2196F3;
        }
        
        .sidebar-item.active {
            background: #2196F3;
            color: white;
            border-left-color: #1976D2;
        }
        
        .content-area {
            flex: 1;
            padding: 20px;
        }
        
        .module {
            display: none;
        }
        
        .module.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #2196F3;
            box-shadow: 0 0 5px rgba(33, 150, 243, 0.3);
        }
        
        .hero-card {
            background: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            transition: all 0.3s;
            position: relative;
        }

        .hero-card:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .hero-card.selected {
            border-color: #2196F3;
            box-shadow: 0 0 10px rgba(33, 150, 243, 0.3);
        }

        .hero-name {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .faction-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            color: white;
        }

        .faction-shu { background: #4CAF50; }
        .faction-wei { background: #2196F3; }
        .faction-wu { background: #FF9800; }
        .faction-qun { background: #9C27B0; }

        .hero-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-bottom: 15px;
        }

        .stat-item {
            text-align: center;
            padding: 8px;
            background: white;
            border-radius: 5px;
            border: 1px solid #eee;
            position: relative;
        }

        .stat-item.editable {
            cursor: pointer;
            border-color: #2196F3;
        }

        .stat-item.editable:hover {
            background: #f0f8ff;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }

        .stat-value {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }

        .stat-input {
            width: 60px;
            text-align: center;
            border: 1px solid #2196F3;
            border-radius: 3px;
            padding: 2px;
            font-size: 14px;
        }

        .hero-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .btn-small {
            padding: 5px 10px;
            font-size: 12px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-edit {
            background: #2196F3;
            color: white;
        }

        .btn-save {
            background: #4CAF50;
            color: white;
        }

        .btn-cancel {
            background: #f44336;
            color: white;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 5px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            transition: width 0.3s;
        }

        .item-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 15px;
            transition: all 0.3s;
        }

        .item-card:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .item-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
        }

        .rarity-1 { background: #9E9E9E; }
        .rarity-2 { background: #4CAF50; }
        .rarity-3 { background: #2196F3; }
        .rarity-4 { background: #9C27B0; }
        .rarity-5 { background: #FF9800; }

        .item-info {
            flex: 1;
        }

        .item-name {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .item-type {
            font-size: 12px;
            color: #666;
        }

        .item-count {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .count-input {
            width: 80px;
            text-align: center;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }

        .visual-editor {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .resource-visual {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .resource-item {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .resource-item::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            transition: all 0.6s;
        }

        .resource-item:hover::before {
            animation: shine 0.6s ease-in-out;
        }

        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .resource-icon {
            font-size: 30px;
            margin-bottom: 10px;
        }

        .resource-label {
            font-size: 14px;
            margin-bottom: 10px;
            opacity: 0.9;
        }

        .resource-value {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .resource-input {
            width: 100%;
            padding: 8px;
            border: none;
            border-radius: 5px;
            text-align: center;
            font-size: 16px;
            background: rgba(255, 255, 255, 0.9);
        }
        
        .file-input {
            display: none;
        }
        
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏛️ 三国存档编辑器</h1>
            <div class="toolbar">
                <button class="btn btn-primary" onclick="openFile()">📁 打开存档</button>
                <button class="btn btn-secondary" onclick="saveFile()">💾 保存存档</button>
                <button class="btn btn-warning" onclick="backupFile()">🔄 备份存档</button>
                <button class="btn btn-secondary" onclick="exportData()">📤 导出数据</button>
            </div>
            <input type="file" id="fileInput" class="file-input" accept=".json" onchange="loadFile(event)">
        </div>
        
        <div class="main-content">
            <div class="sidebar">
                <div class="sidebar-item active" onclick="showModule('resources')">💰 资源管理</div>
                <div class="sidebar-item" onclick="showModule('heroes')">⚔️ 英雄管理</div>
                <div class="sidebar-item" onclick="showModule('items')">🎒 物品管理</div>
                <div class="sidebar-item" onclick="showModule('progress')">📊 进度管理</div>
                <div class="sidebar-item" onclick="showModule('settings')">⚙️ 设置选项</div>
            </div>
            
            <div class="content-area">
                <!-- 资源管理模块 -->
                <div id="resources" class="module active">
                    <h2>💰 资源管理</h2>
                    <div class="visual-editor">
                        <div class="resource-visual">
                            <div class="resource-item">
                                <div class="resource-icon">💰</div>
                                <div class="resource-label">银币</div>
                                <div class="resource-value" id="silverDisplay">0</div>
                                <input type="number" class="resource-input" id="silver" placeholder="输入银币数量" min="0" max="999999999" onchange="updateResourceDisplay('silver')">
                            </div>
                            <div class="resource-item">
                                <div class="resource-icon">🏆</div>
                                <div class="resource-label">威望</div>
                                <div class="resource-value" id="prestigeDisplay">0</div>
                                <input type="number" class="resource-input" id="prestige" placeholder="输入威望值" min="0" max="999999999" onchange="updateResourceDisplay('prestige')">
                            </div>
                            <div class="resource-item">
                                <div class="resource-icon">⚡</div>
                                <div class="resource-label">体力</div>
                                <div class="resource-value" id="energyDisplay">0</div>
                                <input type="number" class="resource-input" id="energy" placeholder="输入体力值" min="0" max="999" onchange="updateResourceDisplay('energy')">
                                <div class="progress-bar">
                                    <div class="progress-fill" id="energyProgress" style="width: 0%"></div>
                                </div>
                            </div>
                            <div class="resource-item">
                                <div class="resource-icon">💎</div>
                                <div class="resource-label">元宝</div>
                                <div class="resource-value" id="goldDisplay">0</div>
                                <input type="number" class="resource-input" id="gold" placeholder="输入元宝数量" min="0" max="999999" onchange="updateResourceDisplay('gold')">
                            </div>
                        </div>
                        <div style="text-align: center;">
                            <button class="btn btn-primary" onclick="updateResources()">💾 保存资源修改</button>
                            <button class="btn btn-secondary" onclick="resetResources()">🔄 重置资源</button>
                        </div>
                    </div>
                </div>
                
                <!-- 英雄管理模块 -->
                <div id="heroes" class="module">
                    <h2>⚔️ 英雄管理</h2>
                    <div class="visual-editor">
                        <div style="margin-bottom: 20px; text-align: center;">
                            <button class="btn btn-secondary" onclick="filterHeroes('all')">全部英雄</button>
                            <button class="btn btn-secondary" onclick="filterHeroes('蜀')">🔴 蜀汉</button>
                            <button class="btn btn-secondary" onclick="filterHeroes('魏')">🔵 曹魏</button>
                            <button class="btn btn-secondary" onclick="filterHeroes('吴')">🟢 东吴</button>
                            <button class="btn btn-secondary" onclick="filterHeroes('群')">🟡 群雄</button>
                        </div>
                        <div id="heroList">
                            <!-- 英雄列表将在这里动态生成 -->
                        </div>
                    </div>
                </div>
                
                <!-- 物品管理模块 -->
                <div id="items" class="module">
                    <h2>🎒 物品管理</h2>
                    <div class="visual-editor">
                        <div style="margin-bottom: 20px; display: flex; gap: 10px; align-items: center;">
                            <input type="text" id="itemSearch" placeholder="🔍 搜索物品..." style="flex: 1; padding: 8px; border: 1px solid #ddd; border-radius: 5px;">
                            <button class="btn btn-primary" onclick="addNewItem()">➕ 添加物品</button>
                            <button class="btn btn-warning" onclick="clearAllItems()">🗑️ 清空背包</button>
                        </div>
                        <div id="itemList">
                            <!-- 物品列表将在这里动态生成 -->
                        </div>
                    </div>
                </div>
                
                <!-- 进度管理模块 -->
                <div id="progress" class="module">
                    <h2>📊 进度管理</h2>
                    <p>副本进度、任务状态等功能开发中...</p>
                </div>
                
                <!-- 设置选项模块 -->
                <div id="settings" class="module">
                    <h2>⚙️ 设置选项</h2>
                    <p>编辑器设置和配置选项...</p>
                </div>
            </div>
        </div>
        
        <div id="status" class="status" style="display: none;"></div>
    </div>

    <script>
        let saveData = null;
        
        // 英雄ID映射表（扩展版）
        const heroDatabase = {
            433: { name: "典韦", faction: "魏", type: "武将", rarity: 5 },
            390: { name: "关羽", faction: "蜀", type: "武将", rarity: 5 },
            358: { name: "赵云", faction: "蜀", type: "武将", rarity: 5 },
            379: { name: "吕布", faction: "群", type: "武将", rarity: 5 },
            548: { name: "鲁肃", faction: "吴", type: "谋士", rarity: 4 },
            392: { name: "太史慈", faction: "吴", type: "武将", rarity: 4 },
            422: { name: "魏延", faction: "蜀", type: "武将", rarity: 4 },
            457: { name: "马超", faction: "蜀", type: "武将", rarity: 5 },
            454: { name: "张辽", faction: "魏", type: "武将", rarity: 5 },
            455: { name: "华佗", faction: "群", type: "医师", rarity: 4 },
            594: { name: "荀攸", faction: "魏", type: "谋士", rarity: 5 },
            525: { name: "陆抗", faction: "吴", type: "武将", rarity: 4 },
            526: { name: "左慈", faction: "群", type: "方士", rarity: 4 }
        };
        
        // 物品ID映射表（扩展版）
        const itemDatabase = {
            11071: { name: "嘲讽卷轴", type: "消耗品", rarity: 2, icon: "🛡️" },
            11002: { name: "初级回春卷轴", type: "恢复道具", rarity: 1, icon: "💊" },
            11012: { name: "中级行军酒", type: "增益道具", rarity: 2, icon: "🍺" },
            11005: { name: "初级治愈卷轴", type: "恢复道具", rarity: 1, icon: "🩹" },
            11003: { name: "中级回春卷轴", type: "恢复道具", rarity: 2, icon: "💊" },
            11601: { name: "初级兽血卷轴", type: "增益道具", rarity: 2, icon: "🩸" },
            11101: { name: "初级禽卷轴", type: "特殊道具", rarity: 2, icon: "🦅" }
        };

        let currentHeroFilter = 'all';
        let editingHero = null;
        
        function showModule(moduleId) {
            // 隐藏所有模块
            document.querySelectorAll('.module').forEach(module => {
                module.classList.remove('active');
            });
            
            // 移除所有侧边栏激活状态
            document.querySelectorAll('.sidebar-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 显示选中模块
            document.getElementById(moduleId).classList.add('active');
            
            // 激活对应侧边栏项
            event.target.classList.add('active');
        }
        
        function openFile() {
            document.getElementById('fileInput').click();
        }
        
        function loadFile(event) {
            const file = event.target.files[0];
            if (!file) return;
            
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    saveData = JSON.parse(e.target.result);
                    loadSaveData();
                    showStatus('存档加载成功！', 'success');
                } catch (error) {
                    showStatus('存档文件格式错误！', 'error');
                }
            };
            reader.readAsText(file);
        }
        
        function loadSaveData() {
            if (!saveData) return;

            // 加载资源数据
            if (saveData.master && saveData.master.values) {
                const values = saveData.master.values.split('H');
                document.getElementById('silver').value = values[0] || 0;
                document.getElementById('prestige').value = values[1] || 0;
                document.getElementById('energy').value = values[2] || 0;
                document.getElementById('gold').value = values[3] || 0;

                // 更新可视化显示
                updateResourceDisplay('silver');
                updateResourceDisplay('prestige');
                updateResourceDisplay('energy');
                updateResourceDisplay('gold');
            }

            // 加载英雄数据
            loadHeroes();

            // 加载物品数据
            loadItems();
        }

        function updateResourceDisplay(type) {
            const input = document.getElementById(type);
            const display = document.getElementById(type + 'Display');
            const value = parseInt(input.value) || 0;

            // 格式化数字显示
            if (value >= 1000000) {
                display.textContent = (value / 1000000).toFixed(1) + 'M';
            } else if (value >= 1000) {
                display.textContent = (value / 1000).toFixed(1) + 'K';
            } else {
                display.textContent = value.toString();
            }

            // 更新体力进度条
            if (type === 'energy') {
                const progress = document.getElementById('energyProgress');
                const percentage = Math.min((value / 999) * 100, 100);
                progress.style.width = percentage + '%';
            }
        }

        function resetResources() {
            if (!saveData || !saveData.master || !saveData.master.values) return;

            const values = saveData.master.values.split('H');
            document.getElementById('silver').value = values[0] || 0;
            document.getElementById('prestige').value = values[1] || 0;
            document.getElementById('energy').value = values[2] || 0;
            document.getElementById('gold').value = values[3] || 0;

            updateResourceDisplay('silver');
            updateResourceDisplay('prestige');
            updateResourceDisplay('energy');
            updateResourceDisplay('gold');

            showStatus('资源已重置到原始值', 'success');
        }
        
        function loadHeroes() {
            const heroList = document.getElementById('heroList');
            heroList.innerHTML = '';

            if (saveData.hero) {
                saveData.hero.forEach((hero, index) => {
                    const heroInfo = heroDatabase[hero.id] || {
                        name: `英雄${hero.id}`,
                        faction: "未知",
                        type: "未知",
                        rarity: 1
                    };
                    const baseStats = hero.base ? hero.base.split('H') : ['0', '0', '0'];

                    // 应用筛选
                    if (currentHeroFilter !== 'all' && heroInfo.faction !== currentHeroFilter) {
                        return;
                    }

                    const heroCard = document.createElement('div');
                    heroCard.className = 'hero-card';
                    heroCard.dataset.heroIndex = index;
                    heroCard.innerHTML = `
                        <div class="hero-name">
                            ${heroInfo.name}
                            <span class="faction-badge faction-${heroInfo.faction.toLowerCase()}">${heroInfo.faction}</span>
                            <span style="font-size: 12px; color: #666;">(${heroInfo.type})</span>
                        </div>
                        <div class="hero-stats">
                            <div class="stat-item editable" onclick="editHeroStat(${index}, 'level')">
                                <div class="stat-label">等级</div>
                                <div class="stat-value" id="hero-${index}-level">${baseStats[0] || 0}</div>
                            </div>
                            <div class="stat-item editable" onclick="editHeroStat(${index}, 'star')">
                                <div class="stat-label">星级</div>
                                <div class="stat-value" id="hero-${index}-star">${baseStats[1] || 0}</div>
                            </div>
                            <div class="stat-item editable" onclick="editHeroStat(${index}, 'experience')">
                                <div class="stat-label">经验</div>
                                <div class="stat-value" id="hero-${index}-experience">${baseStats[2] || 0}</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">稀有度</div>
                                <div class="stat-value">${'⭐'.repeat(heroInfo.rarity)}</div>
                            </div>
                        </div>
                        <div class="hero-actions">
                            <button class="btn-small btn-edit" onclick="quickLevelUp(${index})">⚡ 快速升级</button>
                            <button class="btn-small btn-edit" onclick="maxHero(${index})">🚀 满级满星</button>
                            <button class="btn-small btn-save" onclick="saveHeroChanges(${index})">💾 保存</button>
                        </div>
                    `;
                    heroList.appendChild(heroCard);
                });
            }
        }

        function filterHeroes(faction) {
            currentHeroFilter = faction;
            loadHeroes();

            // 更新按钮状态
            document.querySelectorAll('.btn-secondary').forEach(btn => {
                btn.style.background = '#2196F3';
            });
            event.target.style.background = '#1976D2';
        }

        function editHeroStat(heroIndex, statType) {
            const statElement = document.getElementById(`hero-${heroIndex}-${statType}`);
            const currentValue = statElement.textContent;

            const input = document.createElement('input');
            input.type = 'number';
            input.className = 'stat-input';
            input.value = currentValue;
            input.min = 0;

            if (statType === 'level') input.max = 200;
            if (statType === 'star') input.max = 10;

            input.onblur = () => {
                statElement.textContent = input.value;
                statElement.parentNode.replaceChild(statElement, input);
            };

            input.onkeypress = (e) => {
                if (e.key === 'Enter') {
                    input.blur();
                }
            };

            statElement.parentNode.replaceChild(input, statElement);
            input.focus();
            input.select();
        }

        function quickLevelUp(heroIndex) {
            const levelElement = document.getElementById(`hero-${heroIndex}-level`);
            const starElement = document.getElementById(`hero-${heroIndex}-star`);
            const expElement = document.getElementById(`hero-${heroIndex}-experience`);

            const currentLevel = parseInt(levelElement.textContent) || 0;
            const newLevel = Math.min(currentLevel + 10, 200);
            const newExp = Math.min(parseInt(expElement.textContent) + 100000, 9999999);

            levelElement.textContent = newLevel;
            expElement.textContent = newExp;

            showStatus(`${heroDatabase[saveData.hero[heroIndex].id]?.name || '英雄'} 快速升级完成！`, 'success');
        }

        function maxHero(heroIndex) {
            const levelElement = document.getElementById(`hero-${heroIndex}-level`);
            const starElement = document.getElementById(`hero-${heroIndex}-star`);
            const expElement = document.getElementById(`hero-${heroIndex}-experience`);

            levelElement.textContent = '200';
            starElement.textContent = '10';
            expElement.textContent = '9999999';

            showStatus(`${heroDatabase[saveData.hero[heroIndex].id]?.name || '英雄'} 已满级满星！`, 'success');
        }

        function saveHeroChanges(heroIndex) {
            if (!saveData.hero[heroIndex]) return;

            const level = document.getElementById(`hero-${heroIndex}-level`).textContent;
            const star = document.getElementById(`hero-${heroIndex}-star`).textContent;
            const experience = document.getElementById(`hero-${heroIndex}-experience`).textContent;

            const baseStats = saveData.hero[heroIndex].base ? saveData.hero[heroIndex].base.split('H') : ['0', '0', '0', '0', '0', '0', '0'];
            baseStats[0] = level;
            baseStats[1] = star;
            baseStats[2] = experience;

            saveData.hero[heroIndex].base = baseStats.join('H');

            showStatus(`${heroDatabase[saveData.hero[heroIndex].id]?.name || '英雄'} 数据已保存！`, 'success');
        }
        
        function loadItems() {
            const itemList = document.getElementById('itemList');
            itemList.innerHTML = '';

            if (saveData.bagGood) {
                saveData.bagGood.forEach((item, index) => {
                    const parts = item.split('H');
                    const itemCount = parseInt(parts[0]) || 0;
                    const itemId = parseInt(parts[1]) || 0;
                    const itemInfo = itemDatabase[itemId] || {
                        name: `未知物品${itemId}`,
                        type: "未知",
                        rarity: 1,
                        icon: "❓"
                    };

                    const itemCard = document.createElement('div');
                    itemCard.className = 'item-card';
                    itemCard.innerHTML = `
                        <div class="item-icon rarity-${itemInfo.rarity}">
                            ${itemInfo.icon}
                        </div>
                        <div class="item-info">
                            <div class="item-name">${itemInfo.name}</div>
                            <div class="item-type">${itemInfo.type} | ID: ${itemId}</div>
                        </div>
                        <div class="item-count">
                            <span>数量:</span>
                            <input type="number" class="count-input" value="${itemCount}" min="0" max="999999"
                                   onchange="updateItemCount(${index}, this.value)">
                            <button class="btn-small btn-cancel" onclick="removeItem(${index})">🗑️</button>
                        </div>
                    `;
                    itemList.appendChild(itemCard);
                });
            }

            if (saveData.bagGood && saveData.bagGood.length === 0) {
                itemList.innerHTML = '<div style="text-align: center; color: #666; padding: 40px;">背包为空，点击"添加物品"开始添加</div>';
            }
        }

        function updateItemCount(itemIndex, newCount) {
            if (!saveData.bagGood[itemIndex]) return;

            const count = Math.max(0, parseInt(newCount) || 0);
            if (count === 0) {
                removeItem(itemIndex);
                return;
            }

            const parts = saveData.bagGood[itemIndex].split('H');
            parts[0] = count.toString();
            saveData.bagGood[itemIndex] = parts.join('H');

            showStatus('物品数量已更新', 'success');
        }

        function removeItem(itemIndex) {
            if (!saveData.bagGood[itemIndex]) return;

            const parts = saveData.bagGood[itemIndex].split('H');
            const itemId = parseInt(parts[1]) || 0;
            const itemInfo = itemDatabase[itemId] || { name: `物品${itemId}` };

            if (confirm(`确定要删除 ${itemInfo.name} 吗？`)) {
                saveData.bagGood.splice(itemIndex, 1);
                loadItems();
                showStatus(`${itemInfo.name} 已删除`, 'success');
            }
        }

        function addNewItem() {
            const itemId = prompt('请输入物品ID（例如：11071）:');
            if (!itemId) return;

            const count = prompt('请输入物品数量:', '1');
            if (!count) return;

            const itemCount = Math.max(1, parseInt(count) || 1);
            const newItem = `${itemCount}H${itemId}`;

            if (!saveData.bagGood) {
                saveData.bagGood = [];
            }

            saveData.bagGood.push(newItem);
            loadItems();

            const itemInfo = itemDatabase[itemId] || { name: `物品${itemId}` };
            showStatus(`已添加 ${itemInfo.name} x${itemCount}`, 'success');
        }

        function clearAllItems() {
            if (confirm('确定要清空整个背包吗？此操作不可撤销！')) {
                saveData.bagGood = [];
                loadItems();
                showStatus('背包已清空', 'success');
            }
        }

        // 物品搜索功能
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('itemSearch');
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase();
                    const itemCards = document.querySelectorAll('.item-card');

                    itemCards.forEach(card => {
                        const itemName = card.querySelector('.item-name').textContent.toLowerCase();
                        const itemType = card.querySelector('.item-type').textContent.toLowerCase();

                        if (itemName.includes(searchTerm) || itemType.includes(searchTerm)) {
                            card.style.display = 'flex';
                        } else {
                            card.style.display = 'none';
                        }
                    });
                });
            }
        });
        
        function updateResources() {
            if (!saveData) {
                showStatus('请先加载存档文件！', 'error');
                return;
            }
            
            const silver = document.getElementById('silver').value;
            const prestige = document.getElementById('prestige').value;
            const energy = document.getElementById('energy').value;
            const gold = document.getElementById('gold').value;
            
            // 更新values字段
            if (saveData.master) {
                const values = saveData.master.values.split('H');
                values[0] = silver;
                values[1] = prestige;
                values[2] = energy;
                values[3] = gold;
                saveData.master.values = values.join('H');
                
                showStatus('资源更新成功！', 'success');
            }
        }
        
        function saveFile() {
            if (!saveData) {
                showStatus('没有可保存的数据！', 'error');
                return;
            }
            
            const dataStr = JSON.stringify(saveData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = '存档_edited.json';
            link.click();
            
            showStatus('存档保存成功！', 'success');
        }
        
        function backupFile() {
            if (!saveData) {
                showStatus('没有可备份的数据！', 'error');
                return;
            }
            
            const dataStr = JSON.stringify(saveData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `存档_backup_${new Date().getTime()}.json`;
            link.click();
            
            showStatus('备份创建成功！', 'success');
        }
        
        function exportData() {
            showStatus('导出功能开发中...', 'error');
        }
        
        function showStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
            status.style.display = 'block';
            
            setTimeout(() => {
                status.style.display = 'none';
            }, 3000);
        }
    </script>
</body>
</html>
