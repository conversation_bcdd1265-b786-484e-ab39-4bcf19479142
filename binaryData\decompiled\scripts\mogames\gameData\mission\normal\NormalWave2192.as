package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class NormalWave2192
   {
      
      public function NormalWave2192()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2192);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(823,1900000,8900,650,80,80,300,90,new BossSkillData0(150,{"hurt":50000},5),1006,0);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(275,34000,2950,110,20,50,190,80,0)));
         _loc2_.addEnemy(new WaveEnemyVO(4,new RoleArgVO(275,34000,2950,110,20,50,190,80,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(275,34000,2950,110,20,50,190,80,0)));
         _loc2_.addFu(new BossArgVO(277,500000,5000,450,50,80,150,80,new BossSkillData1(8,{
            "hurt":7750,
            "hurtCount":5
         },2),1002,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(272,34000,2950,110,20,50,190,80,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(273,34000,2950,110,20,50,190,80,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(273,34000,2950,110,20,50,190,80,0)));
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(272,34000,2950,110,20,50,190,80,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(273,34000,2950,110,20,50,190,80,0)));
         _loc2_.addFu(new BossArgVO(278,500000,5000,450,50,80,150,80,new BossSkillData0(250,{
            "hurt":7600,
            "keepTime":3
         },1),1014,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(272,34000,2950,110,20,50,190,80,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(272,34000,2950,110,20,50,190,80,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(273,34000,2950,110,20,50,190,80,0)));
         _loc2_.addFu(new BossArgVO(277,500000,5000,450,50,80,150,80,new BossSkillData1(8,{
            "hurt":7650,
            "hurtCount":5
         },2),1002,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(272,34000,2950,110,20,50,190,80,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(273,34000,2950,110,20,50,190,80,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(272,34000,2950,110,20,50,190,80,0)));
         _loc2_.addFu(new BossArgVO(277,500000,5000,450,50,80,150,80,new BossSkillData0(250,{"hurt":7000},2),1010,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(273,34000,2950,110,20,50,190,80,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(273,34000,2950,110,20,50,190,80,0)));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

