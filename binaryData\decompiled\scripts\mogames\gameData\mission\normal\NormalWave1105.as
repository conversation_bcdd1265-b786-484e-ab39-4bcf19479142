package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class NormalWave1105
   {
      
      public function NormalWave1105()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1105);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(467,92000,800,127,25,32,170,90,new BossSkillData0(100,{"hurt":990},1),0,1);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(104,1692,172,29,20,25,180,80,{
            "rate":200,
            "curePer":40
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(104,1692,172,29,20,25,180,80,{
            "rate":200,
            "curePer":40
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(104,1692,172,29,20,25,180,80,{
            "rate":200,
            "curePer":40
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(104,1692,172,29,20,25,180,80,{
            "rate":200,
            "curePer":40
         })));
         _loc2_.addFu(new BossArgVO(209,20000,840,90,30,30,150,80,new BossSkillData0(80,{
            "hurt":650,
            "roleNum":20
         },2),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(13);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(104,1692,172,29,20,25,180,80,{
            "rate":200,
            "curePer":40
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(104,1692,172,29,20,25,180,80,{
            "rate":200,
            "curePer":40
         })));
         _loc2_.addFu(new BossArgVO(209,20000,840,90,30,30,150,80,new BossSkillData0(80,{
            "hurt":600,
            "roleNum":3
         },2),1004,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(12,new RoleArgVO(104,1692,172,29,20,25,180,80,{
            "rate":200,
            "curePer":40
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(104,1692,172,29,20,25,180,80,{
            "rate":200,
            "curePer":40
         })));
         _loc2_.addFu(new BossArgVO(209,20000,840,90,30,30,150,80,new BossSkillData0(80,{
            "hurt":600,
            "hurtCount":5
         },2),1003,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(104,1692,172,29,20,25,180,80,{
            "rate":200,
            "curePer":40
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(25,new RoleArgVO(104,1817,151,30,20,25,180,80,{
            "rate":200,
            "curePer":40
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

