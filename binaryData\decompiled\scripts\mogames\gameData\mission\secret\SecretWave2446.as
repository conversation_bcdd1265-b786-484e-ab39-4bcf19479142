package mogames.gameData.mission.secret
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class SecretWave2446
   {
      
      public function SecretWave2446()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2446);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(299,7000000,32000,2000,50,100,200,120,new BossSkillData0(150,{
            "hurt":60570,
            "hurtCount":5
         },3),1003,0);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(238,175000,17000,1000,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(238,175000,17000,1000,50,50,170,90,null)));
         _loc2_.addFu(new BossArgVO(293,1200000,12500,2000,30,30,150,50,new BossSkillData0(150,{"hurt":58550},3),1013,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(237,175000,17000,1000,50,50,170,90,null)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(232,150000,100000,100,50,50,170,50,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(236,175000,17000,1000,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(236,175000,17000,1000,50,50,170,90,null)));
         _loc2_.addFu(new BossArgVO(290,1200000,12500,2000,30,30,150,50,new BossSkillData0(150,{"hurt":58550},3),1013,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(236,175000,17000,1000,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(232,150000,100000,100,50,50,170,50,null)));
         _loc2_.addFu(new BossArgVO(290,1200000,12500,2000,30,30,150,50,new BossSkillData0(150,{"hurt":58550},3),1013,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(236,175000,17000,1000,50,50,170,90,null)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(232,150000,100000,100,50,50,170,50,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(232,150000,100000,100,50,50,170,50,null)));
         _loc2_.addFu(new BossArgVO(290,1200000,12500,2000,30,30,150,50,new BossSkillData0(150,{"hurt":58550},3),1013,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(237,175000,17000,1000,50,50,170,90,null)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(232,150000,100000,100,50,50,170,50,null)));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

