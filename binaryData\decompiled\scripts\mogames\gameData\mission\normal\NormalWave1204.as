package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class NormalWave1204
   {
      
      public function NormalWave1204()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1204);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(579,170000,1540,275,80,80,300,90,new BossSkillData0(150,{
            "hurt":3000,
            "keepTime":3
         },2),0,1);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(104,5000,560,85,50,50,200,120,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc2_.addFu(new BossArgVO(208,56000,2200,280,50,80,150,80,new BossSkillData0(150,{
            "hurt":1350,
            "hurtCount":5
         },2),1002,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(104,5000,560,85,50,50,200,120,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(104,5000,560,85,50,50,200,120,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(16,new RoleArgVO(104,5000,560,85,50,50,200,120,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc2_.addFu(new BossArgVO(210,56000,2200,280,50,80,150,80,new BossSkillData0(150,{"hurt":5400},2),1006,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(17,new RoleArgVO(104,5000,560,85,50,50,200,120,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(104,5000,560,85,50,50,200,120,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(104,5000,560,85,50,50,200,120,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc2_.addFu(new BossArgVO(205,56000,2200,280,50,80,150,80,new BossSkillData0(150,{"hurt":5400},2),1006,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(25,new RoleArgVO(104,5000,560,85,50,50,200,120,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(104,5000,560,85,50,50,200,120,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc2_.addFu(new BossArgVO(207,56000,2200,280,50,80,150,80,new BossSkillData0(150,{"hurt":6400},2),1006,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(104,5000,560,85,50,50,200,120,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

