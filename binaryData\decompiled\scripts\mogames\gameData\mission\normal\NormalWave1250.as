package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class NormalWave1250
   {
      
      public function NormalWave1250()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1250);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(635,375000,2710,600,80,80,300,90,new BossSkillData0(150,{
            "hurt":3260,
            "roleNum":30
         },3),0,1);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(110,5300,930,120,50,80,200,120,{
            "rate":100,
            "hurtBei":1.8
         })));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(101,7300,930,120,50,50,200,120,{
            "atkPer":60,
            "keepTime":10
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(110,5300,930,120,50,80,200,120,{
            "rate":100,
            "hurtBei":1.8
         })));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(101,7300,930,120,50,50,200,120,{
            "atkPer":60,
            "keepTime":10
         })));
         _loc2_.addFu(new BossArgVO(202,85000,3300,400,50,80,150,80,new BossSkillData1(10,{"hurt":1750},2),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(112,7300,930,120,50,80,200,120,{
            "rate":150,
            "hurtBei":1.8
         })));
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(101,7300,930,120,50,50,200,120,{
            "atkPer":60,
            "keepTime":10
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(109,7300,930,120,50,80,200,120,{
            "rate":100,
            "hurtPer":60,
            "spdPer":70,
            "keepTime":3
         })));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(101,7300,930,120,50,50,200,120,{
            "atkPer":60,
            "keepTime":10
         })));
         _loc2_.addFu(new BossArgVO(205,85000,3300,400,50,80,150,80,new BossSkillData1(10,{"hurt":1750},2),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(109,7300,930,120,50,80,200,120,{
            "rate":100,
            "hurtPer":60,
            "spdPer":70,
            "keepTime":3
         })));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(101,7300,930,120,50,50,200,120,{
            "atkPer":60,
            "keepTime":10
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(111,4800,850,120,50,80,200,120,{
            "rate":160,
            "keepTime":2
         })));
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(101,7300,930,120,50,50,200,120,{
            "atkPer":60,
            "keepTime":10
         })));
         _loc2_.addFu(new BossArgVO(207,85000,3300,400,50,80,150,80,new BossSkillData0(150,{
            "hurt":1750,
            "hurtCount":5
         },2),1003,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(110,5300,930,120,50,80,200,120,{
            "rate":100,
            "hurtBei":1.8
         })));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(101,7300,930,120,50,50,200,120,{
            "atkPer":60,
            "keepTime":10
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(109,7300,930,120,50,80,200,120,{
            "rate":100,
            "hurtPer":60,
            "spdPer":70,
            "keepTime":3
         })));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(101,7300,930,120,50,50,200,120,{
            "atkPer":60,
            "keepTime":10
         })));
         _loc2_.addFu(new BossArgVO(211,85000,3300,400,50,80,150,80,new BossSkillData0(150,{"hurt":1750},2),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(111,7300,930,120,50,80,200,120,{
            "rate":160,
            "keepTime":2
         })));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(101,7300,930,120,50,50,200,120,{
            "atkPer":60,
            "keepTime":10
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(111,7300,930,120,50,80,200,120,{
            "rate":160,
            "keepTime":2
         })));
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(101,7300,930,120,50,50,200,120,{
            "atkPer":60,
            "keepTime":10
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

