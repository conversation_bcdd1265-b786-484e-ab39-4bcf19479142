<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三国存档编辑器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .header h1 {
            color: #333;
            text-align: center;
            margin-bottom: 15px;
        }
        
        .toolbar {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background: #4CAF50;
            color: white;
        }
        
        .btn-secondary {
            background: #2196F3;
            color: white;
        }
        
        .btn-warning {
            background: #FF9800;
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        .main-content {
            display: flex;
            gap: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .sidebar {
            width: 250px;
            background: #f5f5f5;
            padding: 20px;
        }
        
        .sidebar-item {
            padding: 12px 15px;
            margin-bottom: 5px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
            border-left: 4px solid transparent;
        }
        
        .sidebar-item:hover {
            background: #e0e0e0;
            border-left-color: #2196F3;
        }
        
        .sidebar-item.active {
            background: #2196F3;
            color: white;
            border-left-color: #1976D2;
        }
        
        .content-area {
            flex: 1;
            padding: 20px;
        }
        
        .module {
            display: none;
        }
        
        .module.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #2196F3;
            box-shadow: 0 0 5px rgba(33, 150, 243, 0.3);
        }
        
        .hero-card {
            background: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            transition: all 0.3s;
        }
        
        .hero-card:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }
        
        .hero-name {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .hero-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
        }
        
        .stat-item {
            text-align: center;
            padding: 8px;
            background: white;
            border-radius: 5px;
            border: 1px solid #eee;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .stat-value {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }
        
        .file-input {
            display: none;
        }
        
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏛️ 三国存档编辑器</h1>
            <div class="toolbar">
                <button class="btn btn-primary" onclick="openFile()">📁 打开存档</button>
                <button class="btn btn-secondary" onclick="saveFile()">💾 保存存档</button>
                <button class="btn btn-warning" onclick="backupFile()">🔄 备份存档</button>
                <button class="btn btn-secondary" onclick="exportData()">📤 导出数据</button>
            </div>
            <input type="file" id="fileInput" class="file-input" accept=".json" onchange="loadFile(event)">
        </div>
        
        <div class="main-content">
            <div class="sidebar">
                <div class="sidebar-item active" onclick="showModule('resources')">💰 资源管理</div>
                <div class="sidebar-item" onclick="showModule('heroes')">⚔️ 英雄管理</div>
                <div class="sidebar-item" onclick="showModule('items')">🎒 物品管理</div>
                <div class="sidebar-item" onclick="showModule('progress')">📊 进度管理</div>
                <div class="sidebar-item" onclick="showModule('settings')">⚙️ 设置选项</div>
            </div>
            
            <div class="content-area">
                <!-- 资源管理模块 -->
                <div id="resources" class="module active">
                    <h2>💰 资源管理</h2>
                    <div class="form-group">
                        <label>银币数量</label>
                        <input type="number" id="silver" placeholder="输入银币数量" min="0" max="999999999">
                    </div>
                    <div class="form-group">
                        <label>威望值</label>
                        <input type="number" id="prestige" placeholder="输入威望值" min="0" max="999999999">
                    </div>
                    <div class="form-group">
                        <label>体力值</label>
                        <input type="number" id="energy" placeholder="输入体力值" min="0" max="999">
                    </div>
                    <div class="form-group">
                        <label>元宝数量</label>
                        <input type="number" id="gold" placeholder="输入元宝数量" min="0" max="999999">
                    </div>
                    <button class="btn btn-primary" onclick="updateResources()">更新资源</button>
                </div>
                
                <!-- 英雄管理模块 -->
                <div id="heroes" class="module">
                    <h2>⚔️ 英雄管理</h2>
                    <div id="heroList">
                        <!-- 英雄列表将在这里动态生成 -->
                    </div>
                </div>
                
                <!-- 物品管理模块 -->
                <div id="items" class="module">
                    <h2>🎒 物品管理</h2>
                    <div id="itemList">
                        <!-- 物品列表将在这里动态生成 -->
                    </div>
                </div>
                
                <!-- 进度管理模块 -->
                <div id="progress" class="module">
                    <h2>📊 进度管理</h2>
                    <p>副本进度、任务状态等功能开发中...</p>
                </div>
                
                <!-- 设置选项模块 -->
                <div id="settings" class="module">
                    <h2>⚙️ 设置选项</h2>
                    <p>编辑器设置和配置选项...</p>
                </div>
            </div>
        </div>
        
        <div id="status" class="status" style="display: none;"></div>
    </div>

    <script>
        let saveData = null;
        
        // 英雄ID映射表（简化版）
        const heroNames = {
            433: "典韦",
            390: "关羽", 
            358: "赵云",
            379: "吕布",
            548: "鲁肃",
            392: "太史慈",
            422: "魏延",
            457: "马超",
            454: "张辽",
            455: "华佗"
        };
        
        // 物品ID映射表（简化版）
        const itemNames = {
            11071: "嘲讽卷轴",
            11002: "初级回春卷轴",
            11012: "中级行军酒",
            11005: "初级治愈卷轴",
            11003: "中级回春卷轴",
            11601: "初级兽血卷轴",
            11101: "初级禽卷轴"
        };
        
        function showModule(moduleId) {
            // 隐藏所有模块
            document.querySelectorAll('.module').forEach(module => {
                module.classList.remove('active');
            });
            
            // 移除所有侧边栏激活状态
            document.querySelectorAll('.sidebar-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 显示选中模块
            document.getElementById(moduleId).classList.add('active');
            
            // 激活对应侧边栏项
            event.target.classList.add('active');
        }
        
        function openFile() {
            document.getElementById('fileInput').click();
        }
        
        function loadFile(event) {
            const file = event.target.files[0];
            if (!file) return;
            
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    saveData = JSON.parse(e.target.result);
                    loadSaveData();
                    showStatus('存档加载成功！', 'success');
                } catch (error) {
                    showStatus('存档文件格式错误！', 'error');
                }
            };
            reader.readAsText(file);
        }
        
        function loadSaveData() {
            if (!saveData) return;
            
            // 加载资源数据
            if (saveData.master && saveData.master.values) {
                const values = saveData.master.values.split('H');
                document.getElementById('silver').value = values[0] || 0;
                document.getElementById('prestige').value = values[1] || 0;
                document.getElementById('energy').value = values[2] || 0;
                document.getElementById('gold').value = values[3] || 0;
            }
            
            // 加载英雄数据
            loadHeroes();
            
            // 加载物品数据
            loadItems();
        }
        
        function loadHeroes() {
            const heroList = document.getElementById('heroList');
            heroList.innerHTML = '';
            
            if (saveData.hero) {
                saveData.hero.forEach((hero, index) => {
                    const heroName = heroNames[hero.id] || `英雄${hero.id}`;
                    const baseStats = hero.base ? hero.base.split('H') : ['0', '0', '0'];
                    
                    const heroCard = document.createElement('div');
                    heroCard.className = 'hero-card';
                    heroCard.innerHTML = `
                        <div class="hero-name">${heroName} (ID: ${hero.id})</div>
                        <div class="hero-stats">
                            <div class="stat-item">
                                <div class="stat-label">等级</div>
                                <div class="stat-value">${baseStats[0] || 0}</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">星级</div>
                                <div class="stat-value">${baseStats[1] || 0}</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">经验</div>
                                <div class="stat-value">${baseStats[2] || 0}</div>
                            </div>
                        </div>
                    `;
                    heroList.appendChild(heroCard);
                });
            }
        }
        
        function loadItems() {
            const itemList = document.getElementById('itemList');
            itemList.innerHTML = '';
            
            if (saveData.bagGood) {
                saveData.bagGood.forEach(item => {
                    const parts = item.split('H');
                    const itemId = parts[1];
                    const itemCount = parts[0];
                    const itemName = itemNames[itemId] || `物品${itemId}`;
                    
                    const itemDiv = document.createElement('div');
                    itemDiv.className = 'hero-card';
                    itemDiv.innerHTML = `
                        <div class="hero-name">${itemName}</div>
                        <div>数量: ${itemCount} | ID: ${itemId}</div>
                    `;
                    itemList.appendChild(itemDiv);
                });
            }
        }
        
        function updateResources() {
            if (!saveData) {
                showStatus('请先加载存档文件！', 'error');
                return;
            }
            
            const silver = document.getElementById('silver').value;
            const prestige = document.getElementById('prestige').value;
            const energy = document.getElementById('energy').value;
            const gold = document.getElementById('gold').value;
            
            // 更新values字段
            if (saveData.master) {
                const values = saveData.master.values.split('H');
                values[0] = silver;
                values[1] = prestige;
                values[2] = energy;
                values[3] = gold;
                saveData.master.values = values.join('H');
                
                showStatus('资源更新成功！', 'success');
            }
        }
        
        function saveFile() {
            if (!saveData) {
                showStatus('没有可保存的数据！', 'error');
                return;
            }
            
            const dataStr = JSON.stringify(saveData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = '存档_edited.json';
            link.click();
            
            showStatus('存档保存成功！', 'success');
        }
        
        function backupFile() {
            if (!saveData) {
                showStatus('没有可备份的数据！', 'error');
                return;
            }
            
            const dataStr = JSON.stringify(saveData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `存档_backup_${new Date().getTime()}.json`;
            link.click();
            
            showStatus('备份创建成功！', 'success');
        }
        
        function exportData() {
            showStatus('导出功能开发中...', 'error');
        }
        
        function showStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
            status.style.display = 'block';
            
            setTimeout(() => {
                status.style.display = 'none';
            }, 3000);
        }
    </script>
</body>
</html>
