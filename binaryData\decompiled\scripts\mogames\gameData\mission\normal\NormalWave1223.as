package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class NormalWave1223
   {
      
      public function NormalWave1223()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1223);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(601,275000,2110,500,80,80,300,90,new BossSkillData0(150,{"hurt":5800},3),0,1);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(106,6450,790,110,20,50,190,80,{"atkPer":50})));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(106,6450,790,110,20,50,190,80,{"atkPer":50})));
         _loc2_.addFu(new BossArgVO(212,71000,2800,350,50,80,150,80,new BossSkillData1(12,{
            "hurt":1950,
            "hurtCount":5
         },2),1002,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(106,6450,790,110,20,50,190,80,{"atkPer":50})));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(106,6450,790,110,20,50,190,80,{"atkPer":50})));
         _loc2_.addFu(new BossArgVO(202,71000,2800,350,50,80,150,80,new BossSkillData1(12,{
            "hurt":1950,
            "hurtCount":5
         },2),1002,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(106,6450,790,110,20,50,190,80,{"atkPer":50})));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(106,6450,790,110,20,50,190,80,{"atkPer":50})));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(106,6450,790,110,20,50,190,80,{"atkPer":50})));
         _loc2_.addFu(new BossArgVO(204,71000,2800,350,50,80,150,80,new BossSkillData0(150,{"hurt":2300},2),1010,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(106,6450,790,110,20,50,190,80,{"atkPer":50})));
         _loc2_.addFu(new BossArgVO(206,71000,2800,350,50,80,150,80,new BossSkillData0(150,{
            "hurt":1955,
            "keepTime":3
         },1),1014,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(106,6450,790,110,20,50,190,80,{"atkPer":50})));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(106,6450,790,110,20,50,190,80,{"atkPer":50})));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

