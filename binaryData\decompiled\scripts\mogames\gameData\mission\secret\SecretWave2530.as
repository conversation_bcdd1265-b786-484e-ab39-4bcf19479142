package mogames.gameData.mission.secret
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class SecretWave2530
   {
      
      public function SecretWave2530()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2530);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(796,55000000,180000,100000,50,100,200,120,new BossSkillData0(150,{
            "hurt":250000,
            "hurtCount":5
         },99),1003,0);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(785,1600000,130000,80000,150,150,270,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(785,1600000,130000,80000,150,150,270,90,null)));
         _loc2_.addFu(new BossArgVO(796,20500000,100000,70000,100,200,250,50,new BossSkillData0(150,{"hurt":200000},3),1013,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(785,1600000,130000,80000,150,150,270,90,null)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(232,1200000,300000,50000,150,150,270,50,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(785,1600000,130000,80000,150,150,270,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(785,1600000,130000,80000,150,150,270,90,null)));
         _loc2_.addFu(new BossArgVO(797,20500000,100000,70000,100,200,250,50,new BossSkillData0(150,{"hurt":200000},3),1013,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(785,1600000,130000,80000,150,150,270,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(232,1200000,300000,50000,150,150,270,50,null)));
         _loc2_.addFu(new BossArgVO(795,20500000,100000,70000,100,200,250,50,new BossSkillData0(150,{"hurt":200000},3),1013,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(785,1600000,130000,80000,150,150,270,90,null)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(232,1200000,300000,50000,150,150,270,50,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(232,1200000,300000,50000,150,150,270,50,null)));
         _loc2_.addFu(new BossArgVO(796,20500000,100000,70000,100,200,250,50,new BossSkillData0(150,{"hurt":200000},3),1013,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(785,1600000,130000,80000,150,150,270,90,null)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(232,1200000,300000,50000,150,150,270,50,null)));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

