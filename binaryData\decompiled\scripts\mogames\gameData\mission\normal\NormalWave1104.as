package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class NormalWave1104
   {
      
      public function NormalWave1104()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1104);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(466,92000,790,126,25,31,170,90,new BossSkillData0(80,{
            "hurt":1005,
            "keepTime":3
         },1),0,1);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(108,1600,108,15,20,40,170,80,{
            "rate":120,
            "hurtPer":30,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(108,1600,108,15,20,40,170,80,{
            "rate":120,
            "hurtPer":30,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(101,1692,161,45,25,30,160,80,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(105,1918,161,30,20,25,160,80,{
            "rate":150,
            "curePer":100
         })));
         _loc2_.addFu(new BossArgVO(202,20000,830,90,30,30,150,80,new BossSkillData0(60,{"hurt":800},2),1009,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(105,1918,161,30,20,25,160,80,{
            "rate":150,
            "curePer":100
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(101,1692,161,45,25,30,160,80,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(108,1600,108,15,20,40,170,80,{
            "rate":120,
            "hurtPer":30,
            "keepTime":3
         })));
         _loc2_.addFu(new BossArgVO(203,20000,830,90,30,30,150,80,new BossSkillData0(80,{
            "hurt":490,
            "keepTime":3
         },2),1007,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(105,1918,161,30,20,25,160,80,{
            "rate":150,
            "curePer":100
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(101,1692,161,45,25,30,160,80,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc2_.addFu(new BossArgVO(202,20000,830,90,30,30,150,80,new BossSkillData0(100,{"hurt":700},2),1013,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(108,2000,109,13,20,40,170,80,{
            "rate":120,
            "hurtPer":30,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

