package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class NormalWave1022
   {
      
      public function NormalWave1022()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1022);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(363,30000,97,8,25,25,170,90,new BossSkillData0(50,{
            "hurt":90,
            "roleNum":3
         },1),0,1);
         _loc2_ = new OneWaveVO(45);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(106,407,53,10,30,25,150,60,{"atkPer":14})));
         _loc2_.addFu(new BossArgVO(206,8000,78,13,30,30,150,80,new BossSkillData0(60,{
            "hurt":100,
            "keepTime":3
         },1),1014,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(106,407,53,10,30,25,150,60,{"atkPer":14})));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(106,407,53,10,30,25,150,60,{"atkPer":14})));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(106,407,53,10,30,25,150,60,{"atkPer":14})));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(106,407,53,10,30,25,150,60,{"atkPer":14})));
         _loc2_.addFu(new BossArgVO(206,8000,78,13,30,30,150,80,new BossSkillData0(60,{"hurt":100},1),1013,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(106,407,53,10,30,25,150,60,{"atkPer":14})));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(106,407,53,10,30,25,150,60,{"atkPer":14})));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(106,407,53,10,30,25,150,60,{"atkPer":14})));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(106,407,53,10,30,25,150,60,{"atkPer":14})));
         _loc2_.addFu(new BossArgVO(206,8000,78,13,30,30,150,80,new BossSkillData0(60,{"hurt":100},1),1012,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(106,407,53,10,30,25,150,60,{"atkPer":14})));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

