package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class NormalWave1095
   {
      
      public function NormalWave1095()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1095);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(452,90000,710,117,25,42,170,90,new BossSkillData0(100,{"hurt":950},1),0,1);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(102,1620,171,32,20,40,170,80,{
            "rate":100,
            "hurtBei":1.6,
            "atkPer":50,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(104,1579,175,31,20,40,170,80,{
            "rate":100,
            "curePer":40
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(104,1579,175,31,20,40,170,80,{
            "rate":100,
            "curePer":40
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(106,2027,159,24,20,40,170,80,{"atkPer":15})));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(106,2027,159,24,20,40,170,80,{"atkPer":15})));
         _loc2_.addFu(new BossArgVO(208,20000,750,88,30,30,150,80,new BossSkillData0(80,{
            "hurt":550,
            "keepTime":3
         },2),1007,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(108,1460,106,16,20,40,170,80,{
            "rate":100,
            "hurtPer":20,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(108,1460,93,14,20,40,170,80,{
            "rate":100,
            "hurtPer":20,
            "keepTime":5
         })));
         _loc2_.addFu(new BossArgVO(210,20000,750,88,30,30,150,80,new BossSkillData1(80,{
            "hurt":293,
            "keepTime":8,
            "hurtCount":2
         },2),1015,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(109,1467,163,32,20,40,170,80,{
            "rate":100,
            "hurtPer":60,
            "spdPer":50,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(102,1620,171,32,20,40,170,80,{
            "rate":100,
            "hurtBei":1.6,
            "atkPer":50,
            "keepTime":5
         })));
         _loc2_.addFu(new BossArgVO(205,20000,750,88,30,30,150,80,new BossSkillData0(80,{
            "hurt":560,
            "roleNum":4
         },2),1004,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(45);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(109,1467,163,32,20,40,170,80,{
            "rate":100,
            "hurtPer":60,
            "spdPer":50,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

