package mogames.gameData.mission.secret
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class SecretWave2227
   {
      
      public function SecretWave2227()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2227);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(256,350000,4150,1010,50,100,200,120,new BossSkillData0(150,{"hurt":4960},5),1018,0);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(236,12000,1550,100,50,50,170,90,null)));
         _loc2_.addEnemy(new WaveEnemyVO(4,new RoleArgVO(236,10000,2500,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(2,new RoleArgVO(236,12000,1550,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(236,12000,1550,100,50,50,170,90,null)));
         _loc2_.addFu(new BossArgVO(292,120000,4150,150,30,30,150,50,new BossSkillData0(150,{
            "hurt":4550,
            "roleNum":20
         },2),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(236,12000,1550,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(236,12000,1550,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(236,12000,1550,100,50,50,170,90,null)));
         _loc2_.addFu(new BossArgVO(292,120000,4150,150,30,30,150,50,new BossSkillData0(150,{
            "hurt":4550,
            "roleNum":20
         },2),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(236,12000,1550,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(236,12000,1550,100,50,50,170,90,null)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(236,12000,1550,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(236,12000,1550,100,50,50,170,90,null)));
         _loc2_.addFu(new BossArgVO(292,120000,4150,150,30,30,150,50,new BossSkillData0(150,{
            "hurt":4550,
            "roleNum":20
         },2),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(236,12000,1550,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

