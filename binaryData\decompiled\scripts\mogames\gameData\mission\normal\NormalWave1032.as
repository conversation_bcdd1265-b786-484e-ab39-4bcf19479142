package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class NormalWave1032
   {
      
      public function NormalWave1032()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1032);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(373,40000,98,28,25,25,170,90,new BossSkillData0(80,{"hurt":310},1),0,1);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(109,592,69,14,40,80,200,60,{
            "rate":100,
            "hurtPer":60,
            "spdPer":70,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(109,592,69,14,40,80,200,60,{
            "rate":100,
            "hurtPer":60,
            "spdPer":70,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(109,592,69,14,40,80,200,60,{
            "rate":100,
            "hurtPer":60,
            "spdPer":70,
            "keepTime":3
         })));
         _loc2_.addFu(new BossArgVO(201,13000,115,39,30,30,150,80,new BossSkillData0(80,{"hurt":230},2),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(110,415,43,7,40,80,200,60,{
            "rate":100,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(110,415,43,7,40,80,200,60,{
            "rate":100,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(110,415,43,7,40,80,200,60,{
            "rate":100,
            "hurtBei":1.8
         })));
         _loc2_.addFu(new BossArgVO(211,12000,125,14,30,30,150,80,new BossSkillData0(80,{"hurt":350},2),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(111,395,96,14,40,80,200,60,{
            "rate":160,
            "keepTime":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(111,395,96,14,40,80,200,60,{
            "rate":160,
            "keepTime":2
         })));
         _loc2_.addFu(new BossArgVO(203,10000,165,35,30,30,150,80,new BossSkillData0(80,{"hurt":230},2),1010,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(111,395,96,14,40,80,200,60,{
            "rate":160,
            "keepTime":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(112,434,47,14,40,80,200,60,{
            "rate":150,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

