package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class NormalWave1144
   {
      
      public function NormalWave1144()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1144);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(511,100000,1055,192,25,27,170,90,new BossSkillData0(100,{"hurt":1060},3),0,1);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(104,3250,285,35,40,50,170,80,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(104,3250,285,35,40,50,170,80,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(104,3250,285,35,40,50,170,80,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc2_.addFu(new BossArgVO(212,35000,990,150,30,30,150,80,new BossSkillData0(100,{
            "hurt":650,
            "hurtCount":5
         },2),1002,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(104,3250,285,35,40,50,170,80,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(104,3250,285,35,40,50,170,80,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc2_.addFu(new BossArgVO(212,35000,990,150,30,30,150,80,new BossSkillData0(100,{"hurt":3200},2),1006,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(104,3250,285,35,40,50,170,80,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(104,3250,285,35,40,50,170,80,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc2_.addFu(new BossArgVO(212,35000,990,150,30,30,150,80,new BossSkillData0(100,{"hurt":3200},2),1006,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(25,new RoleArgVO(104,3250,285,35,40,50,170,80,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(104,3250,285,35,40,50,170,80,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc2_.addFu(new BossArgVO(212,35000,990,150,30,30,150,80,new BossSkillData0(100,{"hurt":4200},2),1006,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(104,3250,285,35,40,50,170,80,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

