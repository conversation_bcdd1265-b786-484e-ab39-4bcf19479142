package mogames.gameData.mission.secret
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class SecretWave2491
   {
      
      public function SecretWave2491()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2491);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(797,25000000,100000,50000,50,100,200,120,new BossSkillData0(130,{"hurt":180000},99),1010,0);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(785,700000,60000,20000,150,150,270,90,null)));
         _loc2_.addFu(new BossArgVO(796,8000000,70000,30000,100,200,250,50,new BossSkillData0(150,{
            "hurt":85000,
            "roleNum":20
         },3),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(785,700000,60000,20000,150,150,270,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(785,700000,60000,20000,150,150,270,90,null)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(232,1200000,300000,50000,150,150,270,50,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(785,700000,60000,20000,150,150,270,90,null)));
         _loc2_.addFu(new BossArgVO(797,8000000,70000,30000,100,200,250,50,new BossSkillData0(150,{
            "hurt":85000,
            "roleNum":20
         },3),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(785,700000,60000,20000,150,150,270,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(785,700000,60000,20000,150,150,270,90,null)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(232,1200000,300000,50000,150,150,270,50,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(785,700000,60000,20000,150,150,270,55,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(232,1200000,300000,50000,150,150,270,50,null)));
         _loc2_.addFu(new BossArgVO(795,8000000,70000,30000,100,200,250,50,new BossSkillData0(150,{
            "hurt":85000,
            "roleNum":20
         },3),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(2,new RoleArgVO(785,700000,60000,20000,150,150,270,90,null)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(232,1200000,300000,50000,150,150,270,50,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(2,new RoleArgVO(785,700000,60000,20000,150,150,270,90,null)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(232,1200000,300000,50000,150,150,270,50,null)));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

