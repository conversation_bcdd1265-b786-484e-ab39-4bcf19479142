package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class NormalWave1026
   {
      
      public function NormalWave1026()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1026);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(367,34000,72,12,25,25,170,90,new BossSkillData0(50,{"hurt":290},1),0,1);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(110,325,32,9,30,25,160,80,{
            "rate":100,
            "hurtBei":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(110,325,32,9,30,25,160,80,{
            "rate":100,
            "hurtBei":2
         })));
         _loc2_.addFu(new BossArgVO(210,10000,98,19,30,30,150,80,new BossSkillData0(80,{
            "hurt":150,
            "hurtCount":5
         },1),1002,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(110,325,32,9,30,25,160,80,{
            "rate":100,
            "hurtBei":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(110,325,32,9,30,25,160,80,{
            "rate":100,
            "hurtBei":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(110,325,32,9,30,25,160,80,{
            "rate":100,
            "hurtBei":2
         })));
         _loc2_.addFu(new BossArgVO(210,11000,98,19,30,30,150,80,new BossSkillData0(80,{
            "hurt":103,
            "keepTime":5,
            "hurtCount":3
         },1),1001,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(110,325,32,9,30,25,160,80,{
            "rate":100,
            "hurtBei":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(110,325,32,9,30,25,160,80,{
            "rate":100,
            "hurtBei":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(110,325,32,9,30,25,160,80,{
            "rate":100,
            "hurtBei":2
         })));
         _loc2_.addFu(new BossArgVO(210,10000,98,19,30,30,150,80,new BossSkillData0(80,{"hurt":130},1),1010,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(25,new RoleArgVO(110,325,32,9,30,25,160,80,{
            "rate":100,
            "hurtBei":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(110,325,32,9,30,25,160,80,{
            "rate":100,
            "hurtBei":2
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

