package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class NormalWave2176
   {
      
      public function NormalWave2176()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2176);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(821,1600000,7300,800,80,80,300,90,new BossSkillData0(150,{"hurt":10000},5),1008,0);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(272,31000,2790,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(273,31000,2790,110,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(274,31000,2790,110,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(277,450000,4900,450,50,80,150,80,new BossSkillData1(12,{
            "hurt":6500,
            "hurtCount":5
         },1),1003,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(12,new RoleArgVO(272,31000,2790,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(275,31000,2790,110,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(9,new RoleArgVO(273,31000,2790,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(274,31000,2790,110,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(274,31000,2790,110,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(276,450000,4900,450,50,80,150,80,new BossSkillData0(250,{"hurt":6500},2),1011,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(272,31000,2790,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(4,new RoleArgVO(274,31000,2790,110,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(272,31000,2790,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(275,31000,2790,110,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(275,31000,2790,110,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(276,150000,3700,450,50,80,150,80,new BossSkillData0(220,{
            "hurt":5500,
            "hurtCount":4
         },2),1017,0));
         _loc2_.addFu(new BossArgVO(277,150000,3700,450,50,30,150,80,new BossSkillData1(10,{"hurt":5000},2),1011,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(4,new RoleArgVO(272,31000,2790,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(4,new RoleArgVO(273,31000,2790,110,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(272,31000,2790,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(4,new RoleArgVO(275,31000,2790,110,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

