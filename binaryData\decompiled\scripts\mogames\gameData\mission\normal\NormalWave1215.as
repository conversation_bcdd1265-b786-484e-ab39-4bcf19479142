package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class NormalWave1215
   {
      
      public function NormalWave1215()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1215);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(590,230000,1870,380,80,80,300,90,new BossSkillData0(120,{"hurt":2405},2),0,1);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(105,5900,695,95,50,80,200,120,{
            "rate":150,
            "curePer":100
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(12,new RoleArgVO(105,5900,695,95,50,80,200,120,{
            "rate":150,
            "curePer":100
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(12,new RoleArgVO(101,5900,695,95,50,80,200,120,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc2_.addFu(new BossArgVO(209,66000,2800,320,50,80,150,80,new BossSkillData0(150,{"hurt":1600},2),1013,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(12,new RoleArgVO(108,4000,650,95,50,80,200,120,{
            "rate":120,
            "hurtPer":30,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(108,4000,650,95,50,80,200,120,{
            "rate":120,
            "hurtPer":30,
            "keepTime":3
         })));
         _loc2_.addFu(new BossArgVO(208,66000,2800,320,50,80,150,80,new BossSkillData0(150,{
            "hurt":1490,
            "keepTime":3
         },2),1007,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(101,5900,695,95,50,80,200,120,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(108,4000,650,95,50,80,200,120,{
            "rate":120,
            "hurtPer":30,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(105,5900,695,95,50,80,200,120,{
            "rate":150,
            "curePer":100
         })));
         _loc2_.addFu(new BossArgVO(210,66000,2800,320,50,80,150,80,new BossSkillData0(150,{"hurt":1800},2),1009,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(101,5900,695,95,50,80,200,120,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(108,4000,650,95,50,80,200,120,{
            "rate":120,
            "hurtPer":30,
            "keepTime":3
         })));
         _loc2_.addFu(new BossArgVO(211,66000,2800,320,50,80,150,80,new BossSkillData0(150,{"hurt":1800},2),1009,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

