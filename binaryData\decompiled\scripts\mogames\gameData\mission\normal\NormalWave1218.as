package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class NormalWave1218
   {
      
      public function NormalWave1218()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1218);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(596,250000,1960,420,80,80,300,90,new BossSkillData0(150,{
            "hurt":3800,
            "keepTime":5,
            "hurtCount":3
         },2),0,1);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(109,6200,740,100,50,50,200,120,{
            "rate":150,
            "hurtPer":45,
            "spdPer":50,
            "keepTime":3
         })));
         _loc2_.addFu(new BossArgVO(201,71000,2800,350,30,30,150,80,new BossSkillData0(150,{"hurt":5250},2),1006,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(13,new RoleArgVO(109,6200,740,100,50,50,200,120,{
            "rate":150,
            "hurtPer":45,
            "spdPer":50,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(109,6200,740,100,50,50,200,120,{
            "rate":150,
            "hurtPer":45,
            "spdPer":50,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(109,6200,740,100,50,50,200,120,{
            "rate":150,
            "hurtPer":45,
            "spdPer":50,
            "keepTime":3
         })));
         _loc2_.addFu(new BossArgVO(201,71000,2800,350,50,80,150,80,new BossSkillData1(16,{
            "hurt":2250,
            "hurtCount":5
         },2),1003,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(109,6200,740,100,50,50,200,120,{
            "rate":150,
            "hurtPer":45,
            "spdPer":50,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(109,6200,740,100,50,50,200,120,{
            "rate":150,
            "hurtPer":45,
            "spdPer":50,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(13,new RoleArgVO(109,6200,740,100,50,50,200,120,{
            "rate":150,
            "hurtPer":45,
            "spdPer":50,
            "keepTime":3
         })));
         _loc2_.addFu(new BossArgVO(201,71000,2800,350,50,80,150,80,new BossSkillData1(16,{"hurt":3390},2),1012,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(13,new RoleArgVO(109,6200,740,100,50,50,200,120,{
            "rate":150,
            "hurtPer":45,
            "spdPer":50,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(13,new RoleArgVO(109,6200,740,100,50,50,200,120,{
            "rate":150,
            "hurtPer":45,
            "spdPer":50,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(109,6200,740,100,50,50,200,120,{
            "rate":150,
            "hurtPer":45,
            "spdPer":50,
            "keepTime":3
         })));
         _loc2_.addFu(new BossArgVO(201,71000,2800,350,50,80,150,80,new BossSkillData0(150,{
            "hurt":2390,
            "hurtCount":5
         },2),1019,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

