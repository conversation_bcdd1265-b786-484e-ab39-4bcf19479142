package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class NormalWave1181
   {
      
      public function NormalWave1181()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1181);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(554,107000,1240,226,25,29,170,90,new BossSkillData0(100,{
            "hurt":2260,
            "roleNum":3
         },1),0,1);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(107,4400,460,55,20,50,190,80,{
            "rate":150,
            "hurtBei":1.5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(105,4400,460,55,20,50,190,80,{
            "rate":100,
            "curePer":30
         })));
         _loc2_.addFu(new BossArgVO(201,43000,1500,200,30,30,150,80,new BossSkillData0(100,{
            "hurt":1150,
            "hurtCount":5
         },1),1002,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(102,4400,460,55,20,50,190,80,{
            "rate":100,
            "hurtBei":1.6,
            "atkPer":60,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(105,4400,460,55,20,50,190,80,{
            "rate":100,
            "curePer":30
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(106,4400,460,55,20,50,190,80,{"atkPer":20})));
         _loc2_.addFu(new BossArgVO(202,43000,1500,200,30,30,150,80,new BossSkillData0(100,{"hurt":3700},2),1006,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(106,4400,460,55,20,50,190,80,{"atkPer":20})));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(102,4400,460,55,20,50,190,80,{
            "rate":100,
            "hurtBei":1.6,
            "atkPer":60,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(107,4400,460,55,20,50,190,80,{
            "rate":150,
            "hurtBei":1.5
         })));
         _loc2_.addFu(new BossArgVO(202,43000,1500,200,30,30,150,80,new BossSkillData0(100,{
            "hurt":1255,
            "keepTime":3
         },1),1014,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(107,4400,460,55,20,50,190,80,{
            "rate":150,
            "hurtBei":1.5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(108,2900,460,55,20,50,190,80,{
            "rate":100,
            "hurtPer":20,
            "keepTime":5
         })));
         _loc2_.addFu(new BossArgVO(202,43000,1500,200,30,30,150,80,new BossSkillData0(100,{
            "hurt":1255,
            "keepTime":3
         },1),1014,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

