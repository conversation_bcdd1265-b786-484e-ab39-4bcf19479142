package mogames.gameData.mission.secret
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class SecretWave2300
   {
      
      public function SecretWave2300()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2300);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(299,925000,6900,1030,50,100,200,120,new BossSkillData0(150,{"hurt":5570},3),1008,0);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(238,44000,4300,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(237,44000,4300,100,50,50,170,90,null)));
         _loc2_.addFu(new BossArgVO(292,300000,4750,250,30,30,150,50,new BossSkillData0(100,{
            "hurt":4550,
            "roleNum":20
         },2),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(238,44000,4300,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(232,60000,15000,100,50,50,170,50,null)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(237,44000,4300,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(238,44000,4300,100,50,50,170,90,null)));
         _loc2_.addFu(new BossArgVO(293,300000,4750,250,30,30,150,50,new BossSkillData0(100,{"hurt":4500},2),1013,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(232,60000,15000,100,50,50,170,50,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(236,44000,4300,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(237,44000,4300,100,50,50,170,90,null)));
         _loc2_.addFu(new BossArgVO(292,300000,4750,250,30,30,150,50,new BossSkillData0(100,{"hurt":4500},2),1013,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(4,new RoleArgVO(237,44000,4300,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(232,60000,15000,100,50,50,170,50,null)));
         _loc2_.addFu(new BossArgVO(292,300000,4750,250,30,30,150,50,new BossSkillData0(100,{
            "hurt":4550,
            "roleNum":20
         },2),1005,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

