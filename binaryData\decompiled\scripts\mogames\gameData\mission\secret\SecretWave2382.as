package mogames.gameData.mission.secret
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class SecretWave2382
   {
      
      public function SecretWave2382()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2382);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(258,2800000,10000,20,50,100,200,120,new BossSkillData0(150,{
            "hurt":22570,
            "keepTime":2
         },5),1014,0);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(238,23000,7800,100,50,50,170,90,null)));
         _loc2_.addFu(new BossArgVO(291,700000,9000,1000,30,30,150,50,new BossSkillData0(150,{"hurt":8500},3),1010,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(238,23000,7800,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(238,23000,7800,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(238,23000,7800,100,50,50,170,90,null)));
         _loc2_.addFu(new BossArgVO(291,700000,9000,1000,30,30,150,50,new BossSkillData0(150,{"hurt":8500},3),1013,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(238,23000,7800,100,50,50,170,90,null)));
         _loc2_.addEnemy(new WaveEnemyVO(2,new RoleArgVO(238,23000,7800,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(238,23000,7800,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(238,23000,7800,100,50,50,170,90,null)));
         _loc2_.addFu(new BossArgVO(291,700000,9000,1000,30,30,150,50,new BossSkillData0(150,{"hurt":8500},3),1013,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(238,23000,7800,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(238,23000,7800,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(238,88000,7800,10,50,50,170,90,null)));
         _loc2_.addEnemy(new WaveEnemyVO(2,new RoleArgVO(232,70000,50000,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

