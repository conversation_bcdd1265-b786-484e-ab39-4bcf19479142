package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class NormalWave1072
   {
      
      public function NormalWave1072()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1072);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(427,80000,480,98,25,39,170,90,new BossSkillData0(100,{
            "hurt":680,
            "keepTime":3
         },1),0,1);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(102,1460,150,28,20,50,190,80,{
            "rate":100,
            "hurtBei":1.6,
            "atkPer":60,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(107,1460,150,28,20,50,190,80,{
            "rate":150,
            "hurtBei":1.5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(107,1460,150,28,20,50,190,80,{
            "rate":150,
            "hurtBei":1.5
         })));
         _loc2_.addFu(new BossArgVO(201,20000,628,49,30,30,150,80,new BossSkillData0(80,{
            "hurt":450,
            "hurtCount":5
         },2),1002,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(105,1563,143,28,20,50,190,80,{
            "rate":100,
            "curePer":30
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(105,1863,143,28,20,50,190,80,{
            "rate":100,
            "curePer":30
         })));
         _loc2_.addFu(new BossArgVO(201,20000,628,49,30,30,150,80,new BossSkillData0(80,{
            "hurt":450,
            "hurtCount":5
         },2),1002,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(106,1827,139,22,20,50,190,80,{"atkPer":20})));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(106,1527,139,22,20,50,190,80,{"atkPer":20})));
         _loc2_.addFu(new BossArgVO(202,20000,628,49,30,30,150,80,new BossSkillData0(80,{"hurt":1500},2),1006,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(102,1870,150,28,20,50,190,80,{
            "rate":100,
            "hurtBei":1.6,
            "atkPer":60,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(107,1892,146,25,20,50,190,80,{
            "rate":150,
            "hurtBei":1.5
         })));
         _loc2_.addFu(new BossArgVO(202,20000,629,49,30,30,150,80,new BossSkillData0(80,{
            "hurt":555,
            "keepTime":3
         },2),1014,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(108,1820,93,25,20,50,190,80,{
            "rate":100,
            "hurtPer":20,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

