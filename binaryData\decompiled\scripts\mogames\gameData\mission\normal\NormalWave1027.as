package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class NormalWave1027
   {
      
      public function NormalWave1027()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1027);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(368,20000,120,3,25,25,170,90,new BossSkillData0(100,{
            "hurt":260,
            "roleNum":10,
            "defPer":50,
            "keepTime":5
         },1),0,1,12111);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(2,new RoleArgVO(104,3000,66,13,25,40,150,60,{
            "rate":200,
            "curePer":80
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

