package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class NormalWave2016
   {
      
      public function NormalWave2016()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2016);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(244,450000,3140,600,80,80,300,90,new BossSkillData0(150,{
            "hurt":3500,
            "roleNum":5
         },5),1016,0);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(247,7800,960,130,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(249,7800,960,130,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(250,7800,960,130,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(249,7800,960,130,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(250,7800,960,130,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(249,7800,960,130,50,80,200,120,0)));
         _loc2_.addFu(new BossArgVO(240,91000,3400,400,50,80,150,80,new BossSkillData1(10,{
            "hurt":1450,
            "hurtCount":5
         },2),1002,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(249,7800,960,130,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(249,7800,960,130,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(249,7800,960,130,50,80,200,120,0)));
         _loc2_.addFu(new BossArgVO(240,91000,3400,400,50,80,150,80,new BossSkillData1(10,{
            "hurt":1450,
            "hurtCount":5
         },2),1002,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(248,7800,960,130,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(249,7800,960,130,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(248,7800,960,130,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(249,7800,960,130,50,80,200,120,0)));
         _loc2_.addFu(new BossArgVO(240,91000,3400,400,50,80,150,80,new BossSkillData0(150,{"hurt":6900},3),1006,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(247,7800,960,130,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(249,7800,960,130,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(250,7800,960,130,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(249,7800,960,130,50,80,200,120,0)));
         _loc2_.addFu(new BossArgVO(240,91000,3400,400,50,80,150,80,new BossSkillData0(150,{
            "hurt":1655,
            "keepTime":3
         },2),1014,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(250,7800,960,130,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(249,7800,960,130,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

