package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class NormalWave2081
   {
      
      public function NormalWave2081()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2081);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(244,750000,4200,700,80,80,300,90,new BossSkillData0(150,{"hurt":15000},5),1006,0);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(14,new RoleArgVO(247,12800,1450,110,20,50,190,80,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(247,12800,1450,110,20,50,190,80,0)));
         _loc2_.addFu(new BossArgVO(240,170000,4400,450,50,80,150,80,new BossSkillData1(12,{
            "hurt":3750,
            "hurtCount":5
         },2),1002,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(247,12800,1450,110,20,50,190,80,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(247,12800,1450,110,20,50,190,80,0)));
         _loc2_.addFu(new BossArgVO(240,170000,4400,450,50,80,150,80,new BossSkillData1(12,{
            "hurt":3650,
            "hurtCount":5
         },2),1002,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(247,12800,1450,110,20,50,190,80,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(247,12800,1450,110,20,50,190,80,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(247,12800,1450,110,20,50,190,80,0)));
         _loc2_.addFu(new BossArgVO(240,170000,4400,450,50,80,150,80,new BossSkillData0(150,{"hurt":4000},2),1010,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(247,12800,1450,110,20,50,190,80,0)));
         _loc2_.addFu(new BossArgVO(240,170000,4400,450,50,80,150,80,new BossSkillData0(150,{
            "hurt":3600,
            "keepTime":3
         },1),1014,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(247,12800,1450,110,20,50,190,80,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(247,12800,1450,110,20,50,190,80,0)));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

