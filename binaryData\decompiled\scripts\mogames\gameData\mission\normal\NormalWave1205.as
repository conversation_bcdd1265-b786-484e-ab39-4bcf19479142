package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class NormalWave1205
   {
      
      public function NormalWave1205()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1205);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(580,170000,1570,280,80,80,300,90,new BossSkillData0(150,{"hurt":3100},2),0,1);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(12,new RoleArgVO(108,4000,600,60,50,50,200,120,{
            "rate":80,
            "hurtPer":20,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(108,4000,600,60,50,50,200,120,{
            "rate":80,
            "hurtPer":20,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(108,4000,600,60,50,50,200,120,{
            "rate":80,
            "hurtPer":20,
            "keepTime":3
         })));
         _loc2_.addFu(new BossArgVO(212,56000,2200,280,50,80,150,80,new BossSkillData0(150,{
            "hurt":1600,
            "hurtCount":5
         },1),1003,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(108,4000,600,60,50,50,200,120,{
            "rate":80,
            "hurtPer":20,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(108,4000,600,60,50,50,200,120,{
            "rate":80,
            "hurtPer":20,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(108,4000,600,60,50,50,200,120,{
            "rate":80,
            "hurtPer":20,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(108,4000,600,60,50,50,200,120,{
            "rate":80,
            "hurtPer":20,
            "keepTime":3
         })));
         _loc2_.addFu(new BossArgVO(203,56000,2200,280,50,80,150,80,new BossSkillData0(150,{"hurt":1600},2),1011,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(108,4000,600,60,50,50,200,120,{
            "rate":80,
            "hurtPer":20,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(108,4000,600,60,50,50,200,120,{
            "rate":80,
            "hurtPer":20,
            "keepTime":3
         })));
         _loc2_.addFu(new BossArgVO(208,36000,2200,280,50,80,150,80,new BossSkillData0(80,{
            "hurt":1690,
            "hurtCount":4
         },2),1017,0));
         _loc2_.addFu(new BossArgVO(209,36000,2200,280,50,30,150,80,new BossSkillData0(80,{"hurt":1600},2),1011,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(108,4000,600,60,50,50,200,120,{
            "rate":80,
            "hurtPer":20,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

