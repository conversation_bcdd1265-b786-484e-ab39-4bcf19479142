package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class NormalWave1184
   {
      
      public function NormalWave1184()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1184);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(557,108000,1255,232,25,27,170,90,new BossSkillData0(100,{"hurt":1860},1),0,1);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(104,4500,470,60,50,50,170,80,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc2_.addFu(new BossArgVO(212,44000,1600,200,30,30,150,80,new BossSkillData0(100,{
            "hurt":850,
            "hurtCount":5
         },2),1002,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(50);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(104,4500,470,60,50,50,170,80,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(104,4500,470,60,50,50,170,80,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(16,new RoleArgVO(104,4500,470,60,50,50,170,80,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc2_.addFu(new BossArgVO(212,44000,1600,200,30,30,150,80,new BossSkillData0(100,{"hurt":3400},2),1006,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(17,new RoleArgVO(104,4500,470,60,50,50,170,80,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(45);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(104,4500,470,60,50,50,170,80,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(50);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(104,4500,470,60,50,50,170,80,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc2_.addFu(new BossArgVO(212,44000,1600,200,30,30,150,80,new BossSkillData0(100,{"hurt":3400},2),1006,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(25,new RoleArgVO(104,4500,470,60,50,50,170,80,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(104,4500,470,60,50,50,170,80,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc2_.addFu(new BossArgVO(212,44000,1600,200,30,30,150,80,new BossSkillData0(100,{"hurt":4400},2),1006,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(104,4500,470,60,50,50,170,80,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

