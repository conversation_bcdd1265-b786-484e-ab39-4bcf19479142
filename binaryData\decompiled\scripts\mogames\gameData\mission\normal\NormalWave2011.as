package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class NormalWave2011
   {
      
      public function NormalWave2011()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2011);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(244,425000,3040,600,80,80,300,90,new BossSkillData0(150,{"hurt":3500},5),1011,0);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(248,7600,960,120,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(250,7600,960,120,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(248,7600,960,120,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(250,7600,960,120,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(241,89000,3400,450,50,80,150,80,new BossSkillData1(10,{
            "hurt":1360,
            "hurtCount":5
         },2),1003,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(248,7600,960,120,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(250,7600,960,120,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(250,7600,960,120,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(248,7600,960,120,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(250,7600,960,120,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(248,7600,960,120,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(241,89000,3400,450,50,80,150,80,new BossSkillData1(10,{
            "hurt":1760,
            "roleNum":20
         },2),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(248,7600,960,120,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(250,7600,960,120,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(248,7600,960,120,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(250,7600,960,120,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(250,7600,960,120,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(248,7600,960,120,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(241,89000,3400,450,50,80,150,80,new BossSkillData1(10,{
            "hurt":1760,
            "roleNum":20
         },2),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(248,7600,960,120,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(2,new RoleArgVO(250,7600,960,120,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(248,7600,960,120,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(250,7600,960,120,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(241,89000,3400,450,50,80,150,80,new BossSkillData0(150,{
            "hurt":1530,
            "hurtCount":5
         },2),1003,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

