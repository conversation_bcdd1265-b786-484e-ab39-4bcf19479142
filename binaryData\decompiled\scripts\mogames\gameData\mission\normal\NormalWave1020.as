package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class NormalWave1020
   {
      
      public function NormalWave1020()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1020);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(361,28000,96,0,25,25,170,90,new BossSkillData0(50,{
            "hurt":200,
            "hurtCount":5
         },1),0,1);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(102,350,49,9,18,32,155,70,{
            "rate":100,
            "hurtBei":1.5,
            "atkPer":50,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(102,350,49,9,18,32,155,70,{
            "rate":100,
            "hurtBei":1.5,
            "atkPer":50,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(22);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(102,350,49,9,18,32,155,70,{
            "rate":100,
            "hurtBei":1.5,
            "atkPer":50,
            "keepTime":5
         })));
         _loc2_.addFu(new BossArgVO(204,9000,78,15,30,30,150,80,new BossSkillData0(60,{
            "hurt":110,
            "keepTime":3
         },1),1020,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(27);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(102,350,49,9,18,32,155,70,{
            "rate":100,
            "hurtBei":1.5,
            "atkPer":50,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(102,350,49,9,18,32,155,70,{
            "rate":100,
            "hurtBei":1.5,
            "atkPer":50,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(102,350,49,9,18,32,155,70,{
            "rate":100,
            "hurtBei":1.5,
            "atkPer":50,
            "keepTime":5
         })));
         _loc2_.addFu(new BossArgVO(204,9000,78,15,30,30,150,80,new BossSkillData0(60,{
            "hurt":120,
            "hurtCount":3
         },1),1019,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(102,350,49,9,18,32,155,70,{
            "rate":100,
            "hurtBei":1.5,
            "atkPer":50,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(102,350,49,9,18,32,155,70,{
            "rate":100,
            "hurtBei":1.5,
            "atkPer":50,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(102,350,49,9,18,32,155,70,{
            "rate":100,
            "hurtBei":1.5,
            "atkPer":50,
            "keepTime":5
         })));
         _loc2_.addFu(new BossArgVO(204,9000,78,15,30,30,150,80,new BossSkillData0(60,{"hurt":170},1),1018,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(12,new RoleArgVO(102,350,49,9,18,32,155,70,{
            "rate":100,
            "hurtBei":1.5,
            "atkPer":50,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

