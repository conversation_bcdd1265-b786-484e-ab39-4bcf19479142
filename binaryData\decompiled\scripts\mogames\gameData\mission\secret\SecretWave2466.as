package mogames.gameData.mission.secret
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class SecretWave2466
   {
      
      public function SecretWave2466()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2466);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(796,15000000,80000,30000,50,100,200,120,new BossSkillData0(150,{
            "hurt":180000,
            "keepTime":2
         },99),1014,0);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(785,450000,36000,2000,150,150,270,90,null)));
         _loc2_.addFu(new BossArgVO(796,4000000,20000,5000,100,200,250,50,new BossSkillData0(150,{"hurt":65000},3),1010,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(785,450000,36000,2000,150,150,270,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(785,450000,36000,2000,150,150,270,90,null)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(232,800000,200000,3000,150,150,270,50,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(785,450000,36000,2000,150,150,270,90,null)));
         _loc2_.addFu(new BossArgVO(797,4000000,20000,5000,100,200,250,50,new BossSkillData0(150,{"hurt":65000},3),1013,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(785,450000,36000,2000,150,150,270,90,null)));
         _loc2_.addEnemy(new WaveEnemyVO(2,new RoleArgVO(785,450000,36000,2000,150,150,270,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(785,450000,36000,2000,150,150,270,90,null)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(232,800000,200000,3000,150,150,270,50,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(785,450000,36000,2000,150,150,270,90,null)));
         _loc2_.addFu(new BossArgVO(795,4000000,20000,5000,100,200,250,50,new BossSkillData0(150,{"hurt":65000},3),1013,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(785,450000,36000,2000,150,150,270,90,null)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(232,800000,200000,3000,150,150,270,50,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(785,450000,36000,2000,150,150,270,90,null)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(232,800000,200000,3000,150,150,270,50,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(785,88000,7800,10,150,150,270,90,null)));
         _loc2_.addEnemy(new WaveEnemyVO(2,new RoleArgVO(232,800000,200000,3000,150,150,270,90,null)));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

