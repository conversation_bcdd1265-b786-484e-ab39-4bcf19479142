package mogames.gameData.mission.reward
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import com.mogames.utils.TxtUtil;
   import mogames.gameData.base.RewardProxy;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.MissionProxy;
   
   public class StarRewardVO
   {
      
      private var _areaID:Oint = new Oint();
      
      private var _flag:Oint = new Oint();
      
      private var _need:Oint = new Oint();
      
      private var _rewards:Array;
      
      public function StarRewardVO(param1:int, param2:int, param3:int, param4:Array)
      {
         super();
         MathUtil.saveINT(this._areaID,param1);
         MathUtil.saveINT(this._flag,param2);
         MathUtil.saveINT(this._need,param3);
         this._rewards = param4;
      }
      
      public function get isGet() : Boolean
      {
         return FlagProxy.instance().openFlag.isComplete(MathUtil.loadINT(this._flag));
      }
      
      public function setGet() : void
      {
         FlagProxy.instance().openFlag.setValue(MathUtil.loadINT(this._flag),1);
      }
      
      public function get canGet() : Boolean
      {
         return MissionProxy.instance().curStar(MathUtil.loadINT(this._areaID)) >= this.needStar;
      }
      
      public function get needStar() : int
      {
         return MathUtil.loadINT(this._need);
      }
      
      public function get rewardList() : Array
      {
         return this._rewards;
      }
      
      public function get tip() : String
      {
         var _loc1_:* = TxtUtil.setColor("获得" + this.needStar + "颗星星可领取：","FFFF00") + "<br>" + RewardProxy.instance().parseName1(this._rewards,false);
         if(this.isGet)
         {
            _loc1_ += "<br>（已领取）";
         }
         return _loc1_;
      }
   }
}

