package mogames.gameData.mission.secret
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class SecretWave2259
   {
      
      public function SecretWave2259()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2259);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(258,725000,5550,1040,50,100,200,120,new BossSkillData0(150,{
            "hurt":4570,
            "keepTime":2
         },5),1014,0);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(238,36000,3300,100,50,50,170,90,null)));
         _loc2_.addFu(new BossArgVO(291,250000,4950,150,30,30,150,50,new BossSkillData0(100,{"hurt":4500},2),1010,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(238,36000,3300,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(238,36000,3300,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(238,36000,3300,100,50,50,170,90,null)));
         _loc2_.addEnemy(new WaveEnemyVO(2,new RoleArgVO(238,36000,3300,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(238,36000,3300,100,50,50,170,90,null)));
         _loc2_.addFu(new BossArgVO(291,250000,4950,150,30,30,150,50,new BossSkillData0(100,{"hurt":4500},2),1013,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(238,36000,3300,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(238,36000,3300,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(238,36000,3300,100,50,50,170,90,null)));
         _loc2_.addFu(new BossArgVO(291,250000,4950,150,30,30,150,50,new BossSkillData0(100,{"hurt":4500},2),1013,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(238,36000,3300,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(238,36000,3300,10,50,50,170,90,null)));
         _loc2_.addEnemy(new WaveEnemyVO(2,new RoleArgVO(232,50000,15000,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

