package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class NormalWave1178
   {
      
      public function NormalWave1178()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1178);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(551,107000,1225,220,25,25,170,90,new BossSkillData0(100,{
            "hurt":2850,
            "keepTime":3
         },1),0,1);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(102,4250,445,55,20,20,190,80,{
            "rate":150,
            "hurtBei":2,
            "atkPer":50,
            "keepTime":10
         })));
         _loc2_.addFu(new BossArgVO(201,43000,1500,200,30,30,150,80,new BossSkillData0(100,{"hurt":1250},2),1011,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(13,new RoleArgVO(102,4250,445,55,20,20,190,80,{
            "rate":150,
            "hurtBei":2,
            "atkPer":50,
            "keepTime":10
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(102,4250,445,55,20,20,190,80,{
            "rate":150,
            "hurtBei":2,
            "atkPer":50,
            "keepTime":10
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(102,4250,445,55,20,20,190,80,{
            "rate":150,
            "hurtBei":2,
            "atkPer":50,
            "keepTime":10
         })));
         _loc2_.addFu(new BossArgVO(201,43000,1500,200,30,30,150,80,new BossSkillData0(80,{
            "hurt":1250,
            "hurtCount":5
         },2),1003,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(102,4250,445,55,20,20,190,80,{
            "rate":150,
            "hurtBei":2,
            "atkPer":50,
            "keepTime":10
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(102,4250,445,55,20,20,190,80,{
            "rate":150,
            "hurtBei":2,
            "atkPer":50,
            "keepTime":10
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(13,new RoleArgVO(102,4250,445,55,20,20,190,80,{
            "rate":150,
            "hurtBei":2,
            "atkPer":50,
            "keepTime":10
         })));
         _loc2_.addFu(new BossArgVO(201,43000,1500,200,30,30,150,80,new BossSkillData0(120,{
            "hurt":1390,
            "hurtCount":3
         },2),1017,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(13,new RoleArgVO(102,4250,445,55,20,20,190,80,{
            "rate":150,
            "hurtBei":2,
            "atkPer":50,
            "keepTime":10
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(13,new RoleArgVO(102,4250,445,55,20,20,190,80,{
            "rate":150,
            "hurtBei":2,
            "atkPer":50,
            "keepTime":10
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(102,4250,445,55,20,20,190,80,{
            "rate":150,
            "hurtBei":2,
            "atkPer":50,
            "keepTime":10
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

