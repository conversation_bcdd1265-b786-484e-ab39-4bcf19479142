package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class NormalWave1016
   {
      
      public function NormalWave1016()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1016);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(353,22000,97,13,20,20,160,70,new BossSkillData0(50,{
            "hurt":150,
            "hurtCount":5
         }),0,1);
         _loc2_ = new OneWaveVO(70);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(102,309,48,12,20,40,170,60,{
            "rate":100,
            "hurtBei":1.6,
            "atkPer":30,
            "keepTime":5
         })));
         _loc2_.addFu(new BossArgVO(201,7000,65,20,30,30,150,80,new BossSkillData0(60,{
            "hurt":110,
            "roleNum":4
         }),1004,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(102,309,48,12,20,40,170,60,{
            "rate":100,
            "hurtBei":1.6,
            "atkPer":30,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(9,new RoleArgVO(102,309,48,12,20,40,170,60,{
            "rate":100,
            "hurtBei":1.6,
            "atkPer":30,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(102,309,48,12,20,40,170,60,{
            "rate":100,
            "hurtBei":1.6,
            "atkPer":30,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(102,309,48,12,20,40,170,60,{
            "rate":100,
            "hurtBei":1.6,
            "atkPer":30,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(102,309,48,12,20,40,170,60,{
            "rate":100,
            "hurtBei":1.6,
            "atkPer":30,
            "keepTime":5
         })));
         _loc2_.addFu(new BossArgVO(201,7000,78,20,30,30,150,80,new BossSkillData0(60,{
            "hurt":100,
            "keepTime":3
         },1),1007,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(102,309,48,12,20,40,170,60,{
            "rate":100,
            "hurtBei":1.6,
            "atkPer":30,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(102,309,48,12,20,40,170,60,{
            "rate":150,
            "hurtBei":1.6,
            "atkPer":30,
            "keepTime":5
         })));
         _loc2_.addFu(new BossArgVO(202,7000,78,25,30,30,150,80,new BossSkillData1(20,{
            "hurt":53,
            "keepTime":8,
            "hurtCount":2
         },1),1015,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(102,309,48,12,20,40,170,60,{
            "rate":100,
            "hurtBei":1.6,
            "atkPer":30,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(102,309,48,12,20,40,170,60,{
            "rate":100,
            "hurtBei":1.6,
            "atkPer":30,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

