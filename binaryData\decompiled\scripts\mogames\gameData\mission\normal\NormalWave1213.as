package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class NormalWave1213
   {
      
      public function NormalWave1213()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1213);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(588,230000,1810,360,80,80,300,90,new BossSkillData0(150,{
            "hurt":3000,
            "roleNum":5
         },2),0,1);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(12,new RoleArgVO(102,5700,665,95,50,80,200,120,{
            "rate":100,
            "hurtBei":1.6,
            "atkPer":60,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(107,5700,665,95,50,80,200,120,{
            "rate":150,
            "hurtBei":1.5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(107,5700,665,95,50,80,200,120,{
            "rate":150,
            "hurtBei":1.5
         })));
         _loc2_.addFu(new BossArgVO(203,66000,2800,320,50,80,150,80,new BossSkillData0(150,{
            "hurt":1450,
            "hurtCount":5
         },2),1002,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(105,5700,665,95,50,80,200,120,{
            "rate":100,
            "curePer":30
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(105,5700,665,95,50,80,200,120,{
            "rate":100,
            "curePer":30
         })));
         _loc2_.addFu(new BossArgVO(205,66000,2800,320,50,80,150,80,new BossSkillData0(150,{
            "hurt":1450,
            "hurtCount":5
         },2),1002,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(106,5700,665,95,50,80,200,120,{"atkPer":20})));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(106,5700,665,95,50,80,200,120,{"atkPer":20})));
         _loc2_.addFu(new BossArgVO(207,66000,2800,320,50,80,150,80,new BossSkillData0(150,{"hurt":6900},3),1006,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(102,5700,665,95,50,80,200,120,{
            "rate":100,
            "hurtBei":1.6,
            "atkPer":60,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(107,5700,665,95,50,80,200,120,{
            "rate":150,
            "hurtBei":1.5
         })));
         _loc2_.addFu(new BossArgVO(209,66000,2800,320,50,80,150,80,new BossSkillData0(150,{
            "hurt":1655,
            "keepTime":3
         },2),1014,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(108,5700,665,95,50,80,200,120,{
            "rate":100,
            "hurtPer":20,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

