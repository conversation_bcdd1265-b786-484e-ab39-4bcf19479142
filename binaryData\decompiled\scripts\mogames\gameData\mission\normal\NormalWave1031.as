package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class NormalWave1031
   {
      
      public function NormalWave1031()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1031);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(372,39000,98,17,25,25,170,90,new BossSkillData0(80,{"hurt":250},1),0,1);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(102,560,68,14,20,50,190,60,{
            "rate":100,
            "hurtBei":1.6,
            "atkPer":60,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(102,560,68,14,20,50,190,60,{
            "rate":100,
            "hurtBei":1.6,
            "atkPer":60,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(105,560,68,14,20,50,190,60,{
            "rate":100,
            "curePer":30
         })));
         _loc2_.addFu(new BossArgVO(201,13000,88,18,30,30,150,80,new BossSkillData0(80,{
            "hurt":150,
            "hurtCount":5
         },2),1002,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(105,653,64,14,20,50,190,60,{
            "rate":100,
            "curePer":30
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(106,553,67,14,20,50,190,60,{"atkPer":20})));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(106,653,67,14,20,50,190,60,{"atkPer":20})));
         _loc2_.addFu(new BossArgVO(202,13000,78,19,30,30,150,80,new BossSkillData0(80,{"hurt":190},2),1006,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(107,530,67,14,20,50,190,60,{
            "rate":150,
            "hurtBei":1.5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(107,530,67,14,20,50,190,60,{
            "rate":150,
            "hurtBei":1.5
         })));
         _loc2_.addFu(new BossArgVO(202,13000,99,12,30,30,150,80,new BossSkillData0(80,{
            "hurt":155,
            "keepTime":3
         },2),1014,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(107,530,67,14,20,50,190,60,{
            "rate":150,
            "hurtBei":1.5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(108,420,59,14,20,50,190,60,{
            "rate":100,
            "hurtPer":20,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

