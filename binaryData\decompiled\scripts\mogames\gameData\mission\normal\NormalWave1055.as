package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class NormalWave1055
   {
      
      public function NormalWave1055()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1055);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(410,63000,320,58,25,42,170,90,new BossSkillData0(100,{
            "hurt":750,
            "hurtCount":3
         },1),0,1);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(101,889,113,36,25,30,160,80,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(111,960,164,12,25,30,160,80,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(111,960,164,12,25,30,160,80,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc2_.addFu(new BossArgVO(203,17000,485,23,30,30,150,80,new BossSkillData0(80,{"hurt":310},1),1013,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(111,1260,164,12,25,30,160,80,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(110,1291,67,18,25,30,160,80,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(110,891,67,18,25,30,160,80,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc2_.addFu(new BossArgVO(211,17000,475,24,30,30,150,80,new BossSkillData0(80,{"hurt":560},1),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(110,1091,67,18,25,30,160,80,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(108,1260,80,12,25,30,160,80,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(50);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(101,1089,113,36,25,30,160,80,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc2_.addFu(new BossArgVO(204,17000,475,23,30,30,150,80,new BossSkillData0(80,{
            "hurt":430,
            "roleNum":20
         },1),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(108,860,80,12,25,30,160,80,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

