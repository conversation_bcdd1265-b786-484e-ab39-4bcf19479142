package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class NormalWave1071
   {
      
      public function NormalWave1071()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1071);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(426,79000,470,97,25,38,170,90,new BossSkillData0(100,{"hurt":1850},1),0,1);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(104,1409,153,26,20,45,160,80,{
            "rate":200,
            "curePer":40
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(104,1409,153,26,20,45,160,80,{
            "rate":200,
            "curePer":40
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(104,1409,153,26,20,45,160,80,{
            "rate":200,
            "curePer":40
         })));
         _loc2_.addFu(new BossArgVO(201,20000,578,48,30,30,150,80,new BossSkillData0(100,{"hurt":630},2),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(111,1290,184,14,20,45,160,80,{
            "rate":100,
            "keepTime":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(111,1790,184,14,20,45,160,80,{
            "rate":100,
            "keepTime":2
         })));
         _loc2_.addFu(new BossArgVO(203,20000,518,48,30,30,150,80,new BossSkillData1(10,{
            "hurt":460,
            "hurtCount":5
         },2),1003,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(102,1790,129,24,20,45,160,80,{
            "rate":100,
            "hurtBei":1.5,
            "atkPer":50,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(102,1790,129,24,20,45,160,80,{
            "rate":100,
            "hurtBei":1.5,
            "atkPer":50,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(102,1790,129,24,20,45,160,80,{
            "rate":100,
            "hurtBei":1.5,
            "atkPer":50,
            "keepTime":5
         })));
         _loc2_.addFu(new BossArgVO(205,20000,598,48,35,30,150,80,new BossSkillData1(10,{
            "hurt":400,
            "hurtCount":5
         },2),1003,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(8);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(101,1709,123,36,20,45,160,80,{
            "atkPer":60,
            "keepTime":10
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(101,1709,123,36,20,45,160,80,{
            "atkPer":60,
            "keepTime":10
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

