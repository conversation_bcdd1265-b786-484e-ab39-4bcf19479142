package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class NormalWave1224
   {
      
      public function NormalWave1224()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1224);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(602,275000,2140,500,80,80,300,90,new BossSkillData0(150,{
            "hurt":3000,
            "keepTime":3
         },3),0,1);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(107,6500,800,110,50,50,200,120,{
            "rate":150,
            "hurtBei":2
         })));
         _loc2_.addFu(new BossArgVO(208,71000,2800,350,50,80,150,80,new BossSkillData0(150,{
            "hurt":1350,
            "hurtCount":5
         },2),1002,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(107,6500,800,110,50,50,200,120,{
            "rate":150,
            "hurtBei":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(107,6500,800,110,50,50,200,120,{
            "rate":150,
            "hurtBei":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(16,new RoleArgVO(107,6500,800,110,50,50,200,120,{
            "rate":150,
            "hurtBei":2
         })));
         _loc2_.addFu(new BossArgVO(210,71000,2800,350,50,80,150,80,new BossSkillData1(12,{"hurt":5400},2),1006,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(17,new RoleArgVO(107,6500,800,110,50,50,200,120,{
            "rate":150,
            "hurtBei":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(107,6500,800,110,50,50,200,120,{
            "rate":150,
            "hurtBei":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(107,6500,800,110,50,50,200,120,{
            "rate":150,
            "hurtBei":2
         })));
         _loc2_.addFu(new BossArgVO(205,71000,2800,350,50,80,150,80,new BossSkillData1(12,{"hurt":5400},2),1006,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(25,new RoleArgVO(107,6500,800,110,50,50,200,120,{
            "rate":150,
            "hurtBei":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(107,6500,800,110,50,50,200,120,{
            "rate":150,
            "hurtBei":2
         })));
         _loc2_.addFu(new BossArgVO(207,71000,2800,350,50,80,150,80,new BossSkillData0(150,{"hurt":6400},2),1006,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(107,6500,800,110,50,50,200,120,{
            "rate":150,
            "hurtBei":2
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

