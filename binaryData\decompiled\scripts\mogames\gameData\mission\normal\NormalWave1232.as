package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class NormalWave1232
   {
      
      public function NormalWave1232()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1232);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(610,300000,2380,550,80,80,300,90,new BossSkillData0(150,{
            "hurt":2800,
            "keepTime":8,
            "hurtCount":6
         },3),0,1);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(102,6900,870,120,50,80,200,120,{
            "rate":150,
            "hurtBei":1.6,
            "atkPer":50,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(106,6900,870,120,50,80,200,120,{"atkPer":15})));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(111,5000,870,120,50,80,200,120,{
            "rate":150,
            "keepTime":2
         })));
         _loc2_.addFu(new BossArgVO(204,76000,3000,380,50,80,150,80,new BossSkillData0(150,{
            "hurt":1660,
            "roleNum":5
         },2),1016,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(105,6900,870,120,50,80,200,120,{
            "rate":150,
            "curePer":100
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(105,6900,870,120,50,80,200,120,{
            "rate":150,
            "curePer":100
         })));
         _loc2_.addFu(new BossArgVO(206,76000,3000,380,50,80,150,80,new BossSkillData1(10,{
            "hurt":1650,
            "roleNum":20
         },2),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(25,new RoleArgVO(111,5000,870,120,50,80,200,120,{
            "rate":150,
            "keepTime":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(102,6900,870,120,50,80,200,120,{
            "rate":150,
            "hurtBei":1.6,
            "atkPer":50,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(106,6900,870,120,50,80,200,120,{"atkPer":15})));
         _loc2_.addFu(new BossArgVO(208,76000,3000,380,50,80,150,80,new BossSkillData1(10,{"hurt":1190},2),1011,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(111,5000,870,120,50,80,200,120,{
            "rate":150,
            "keepTime":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(111,5000,870,120,50,80,200,120,{
            "rate":150,
            "keepTime":2
         })));
         _loc2_.addFu(new BossArgVO(210,76000,3000,380,50,80,150,80,new BossSkillData0(150,{
            "hurt":1560,
            "roleNum":5
         },2),1016,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

