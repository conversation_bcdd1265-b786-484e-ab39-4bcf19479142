package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class NormalWave1222
   {
      
      public function NormalWave1222()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1222);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(600,250000,2080,500,80,80,300,90,new BossSkillData0(150,{
            "hurt":3000,
            "roleNum":30
         },3),0,1);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(105,6400,780,100,50,50,200,120,{
            "rate":50,
            "curePer":30
         })));
         _loc2_.addFu(new BossArgVO(210,71000,2800,350,50,80,150,80,new BossSkillData0(150,{
            "hurt":2300,
            "roleNum":20
         },2),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(105,6400,780,100,50,50,200,120,{
            "rate":50,
            "curePer":30
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(105,6400,780,100,50,50,200,120,{
            "rate":50,
            "curePer":30
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(105,6400,780,100,50,50,200,120,{
            "rate":50,
            "curePer":30
         })));
         _loc2_.addFu(new BossArgVO(201,71000,2800,350,50,80,150,80,new BossSkillData1(12,{"hurt":2400},2),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(105,6400,780,100,50,50,200,120,{
            "rate":50,
            "curePer":30
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(105,6400,780,100,50,50,200,120,{
            "rate":50,
            "curePer":30
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(105,6400,780,100,50,50,200,120,{
            "rate":50,
            "curePer":30
         })));
         _loc2_.addFu(new BossArgVO(203,71000,2800,350,50,80,150,80,new BossSkillData1(12,{
            "hurt":2150,
            "roleNum":20
         },2),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(105,6400,780,100,50,50,200,120,{
            "rate":50,
            "curePer":30
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(25,new RoleArgVO(105,6400,780,100,50,50,200,120,{
            "rate":50,
            "curePer":30
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(105,6400,780,100,50,50,200,120,{
            "rate":50,
            "curePer":30
         })));
         _loc2_.addFu(new BossArgVO(205,71000,2800,350,50,80,150,80,new BossSkillData0(150,{"hurt":2400},2),1008,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

