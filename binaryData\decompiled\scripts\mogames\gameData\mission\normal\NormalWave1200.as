package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class NormalWave1200
   {
      
      public function NormalWave1200()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1200);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(575,140000,1420,260,80,80,300,90,new BossSkillData0(100,{
            "hurt":2800,
            "hurtCount":5
         },2),0,1);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(102,4600,490,80,50,50,200,120,{
            "rate":150,
            "hurtBei":1.6,
            "atkPer":50,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(106,4600,490,80,50,50,200,120,{"atkPer":15})));
         _loc2_.addFu(new BossArgVO(209,51000,2000,240,50,80,150,80,new BossSkillData0(100,{
            "hurt":2300,
            "roleNum":20
         },2),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(13,new RoleArgVO(111,3500,700,80,50,50,200,120,{
            "rate":150,
            "keepTime":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(25,new RoleArgVO(111,3500,700,80,50,50,200,120,{
            "rate":150,
            "keepTime":2
         })));
         _loc2_.addFu(new BossArgVO(210,51000,2000,240,50,80,150,80,new BossSkillData0(100,{
            "hurt":2360,
            "roleNum":4
         },2),1016,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(105,4600,490,80,50,50,200,120,{
            "rate":150,
            "curePer":100
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(111,3500,700,80,50,50,200,120,{
            "rate":150,
            "keepTime":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(13,new RoleArgVO(106,4600,490,80,50,50,200,120,{"atkPer":15})));
         _loc2_.addFu(new BossArgVO(211,51000,2000,240,50,80,150,80,new BossSkillData0(100,{
            "hurt":2260,
            "roleNum":4
         },2),1016,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(102,4600,490,80,50,50,200,120,{
            "rate":150,
            "hurtBei":1.6,
            "atkPer":50,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(105,4600,490,80,50,50,200,120,{
            "rate":150,
            "curePer":100
         })));
         _loc2_.addFu(new BossArgVO(212,51000,2000,240,50,80,150,80,new BossSkillData0(100,{
            "hurt":2200,
            "roleNum":20
         },2),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(25,new RoleArgVO(111,3500,700,80,50,50,200,120,{
            "rate":150,
            "keepTime":2
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

