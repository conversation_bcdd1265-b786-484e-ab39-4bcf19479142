package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class NormalWave2037
   {
      
      public function NormalWave2037()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2037);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(812,525000,3560,600,80,80,300,90,new BossSkillData0(150,{
            "hurt":4500,
            "hurtCount":4
         },5),1017,0);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(247,8500,1020,130,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(247,8500,1020,130,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(247,8500,1020,130,50,80,200,120,0)));
         _loc2_.addFu(new BossArgVO(240,105000,3400,400,50,80,150,80,new BossSkillData1(10,{"hurt":2000},2),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(248,8500,1020,130,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(248,8500,1020,130,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(248,8500,1020,130,50,80,200,120,0)));
         _loc2_.addFu(new BossArgVO(241,105000,3400,400,50,80,150,80,new BossSkillData1(10,{"hurt":2000},2),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(249,8500,1020,130,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(249,8500,1020,130,50,80,200,120,0)));
         _loc2_.addFu(new BossArgVO(242,105000,3400,400,50,80,150,80,new BossSkillData0(150,{"hurt":2000},2),1010,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(249,8500,1020,130,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(250,8500,1020,130,50,80,200,120,0)));
         _loc2_.addFu(new BossArgVO(240,105000,3400,400,50,80,150,80,new BossSkillData0(150,{"hurt":2000},2),1008,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

