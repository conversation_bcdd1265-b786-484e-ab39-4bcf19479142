package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class NormalWave1209
   {
      
      public function NormalWave1209()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1209);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(584,200000,1690,320,80,80,300,90,new BossSkillData0(150,{"hurt":3550},2),0,1);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(104,5400,620,90,50,50,200,120,{
            "rate":200,
            "curePer":40
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(102,5400,620,90,50,50,200,120,{
            "rate":100,
            "hurtBei":1.5,
            "atkPer":50,
            "keepTime":5
         })));
         _loc2_.addFu(new BossArgVO(203,61000,2400,320,50,80,150,80,new BossSkillData1(10,{
            "hurt":1460,
            "hurtCount":5
         },2),1003,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(12,new RoleArgVO(104,5400,620,90,50,50,200,120,{
            "rate":200,
            "curePer":40
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(111,4000,750,90,50,50,200,120,{
            "rate":100,
            "keepTime":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(12,new RoleArgVO(104,5400,620,90,50,50,200,120,{
            "rate":200,
            "curePer":40
         })));
         _loc2_.addFu(new BossArgVO(206,61000,2400,320,50,80,150,80,new BossSkillData0(150,{"hurt":1630},2),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(111,4000,750,90,50,50,200,120,{
            "rate":100,
            "keepTime":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(102,5400,620,90,50,50,200,120,{
            "rate":100,
            "hurtBei":1.5,
            "atkPer":50,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(102,5400,620,90,50,50,200,120,{
            "rate":100,
            "hurtBei":1.5,
            "atkPer":50,
            "keepTime":5
         })));
         _loc2_.addFu(new BossArgVO(208,61000,2400,320,50,80,150,80,new BossSkillData0(150,{"hurt":1630},2),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(111,4000,750,90,50,50,200,120,{
            "rate":100,
            "keepTime":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(50);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(101,5400,620,90,50,50,200,120,{
            "atkPer":60,
            "keepTime":10
         })));
         _loc2_.addFu(new BossArgVO(210,31000,2400,320,50,80,150,80,new BossSkillData1(10,{
            "hurt":1300,
            "hurtCount":5
         },2),1003,0));
         _loc2_.addFu(new BossArgVO(203,31000,2400,320,50,80,150,80,new BossSkillData1(12,{
            "hurt":1300,
            "hurtCount":5
         },2),1003,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

