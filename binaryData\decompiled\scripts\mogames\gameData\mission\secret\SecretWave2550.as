package mogames.gameData.mission.secret
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class SecretWave2550
   {
      
      public function SecretWave2550()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2550);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(796,100000000,300000,300000,50,100,200,120,new BossSkillData0(150,{
            "hurt":500000,
            "keepTime":2
         },99),1014,0);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(785,2500000,180000,150000,150,150,270,90,null)));
         _loc2_.addFu(new BossArgVO(796,3000000,250000,200000,100,200,250,50,new BossSkillData0(150,{"hurt":200000},3),1010,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(785,2500000,180000,150000,150,150,270,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(785,2500000,180000,150000,150,150,270,90,null)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(232,4000000,1000000,350000,150,150,270,50,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(785,2500000,180000,150000,150,150,270,90,null)));
         _loc2_.addFu(new BossArgVO(797,3000000,250000,200000,100,200,250,50,new BossSkillData0(150,{"hurt":200000},3),1013,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(785,2500000,180000,150000,150,150,270,90,null)));
         _loc2_.addEnemy(new WaveEnemyVO(2,new RoleArgVO(785,2500000,180000,150000,150,150,270,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(785,2500000,180000,150000,150,150,270,90,null)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(232,4000000,1000000,350000,150,150,270,50,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(785,2500000,180000,150000,150,150,270,90,null)));
         _loc2_.addFu(new BossArgVO(795,3000000,250000,200000,100,200,250,50,new BossSkillData0(150,{"hurt":200000},3),1013,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(785,2500000,180000,150000,150,150,270,90,null)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(232,4000000,1000000,350000,150,150,270,50,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(785,2500000,180000,150000,150,150,270,90,null)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(232,4000000,1000000,350000,150,150,270,50,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(785,88000,7800,10,150,150,270,90,null)));
         _loc2_.addEnemy(new WaveEnemyVO(2,new RoleArgVO(232,4000000,1000000,350000,150,150,270,90,null)));
         _loc2_.addFu(new BossArgVO(795,3000000,250000,200000,100,200,250,50,new BossSkillData0(150,{"hurt":200000},3),1013,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

