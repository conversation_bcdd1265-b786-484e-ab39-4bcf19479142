package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class NormalWave1066
   {
      
      public function NormalWave1066()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1066);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(421,74000,420,92,25,33,170,90,new BossSkillData0(80,{"hurt":900},1),0,1);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(109,1467,139,24,20,40,170,80,{
            "rate":100,
            "hurtPer":60,
            "spdPer":50,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(102,1440,146,27,20,40,170,80,{
            "rate":100,
            "hurtBei":1.6,
            "atkPer":50,
            "keepTime":5
         })));
         _loc2_.addFu(new BossArgVO(205,20000,425,44,30,30,150,80,new BossSkillData0(80,{
            "hurt":460,
            "roleNum":4
         },2),1004,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(102,1440,146,27,20,40,170,80,{
            "rate":100,
            "hurtBei":1.6,
            "atkPer":50,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(104,1405,149,25,20,40,170,80,{
            "rate":100,
            "curePer":40
         })));
         _loc2_.addFu(new BossArgVO(208,20000,578,44,30,30,150,80,new BossSkillData0(80,{
            "hurt":450,
            "keepTime":3
         },2),1007,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(104,1405,149,25,20,40,170,80,{
            "rate":100,
            "curePer":40
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(106,1487,135,20,20,40,170,80,{"atkPer":15})));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(106,1887,135,20,20,40,170,80,{"atkPer":15})));
         _loc2_.addFu(new BossArgVO(208,20000,578,44,30,30,150,80,new BossSkillData0(80,{
            "hurt":550,
            "keepTime":3
         },2),1007,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(108,1420,90,13,20,40,170,80,{
            "rate":100,
            "hurtPer":20,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(108,1420,90,13,20,40,170,80,{
            "rate":100,
            "hurtPer":20,
            "keepTime":5
         })));
         _loc2_.addFu(new BossArgVO(210,20000,538,44,30,30,150,80,new BossSkillData1(10,{
            "hurt":253,
            "keepTime":8,
            "hurtCount":3
         },2),1015,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(109,1467,139,24,20,40,170,80,{
            "rate":100,
            "hurtPer":60,
            "spdPer":50,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

