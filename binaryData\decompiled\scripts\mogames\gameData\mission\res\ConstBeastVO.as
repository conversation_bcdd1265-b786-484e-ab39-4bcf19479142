package mogames.gameData.mission.res
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   
   public class ConstBeastVO extends ConstGatherVO
   {
      
      protected var _hp:Oint = new Oint();
      
      public var speed:Number;
      
      public function ConstBeastVO(param1:int, param2:int, param3:int, param4:Number, param5:String)
      {
         super(param1,param2,param5);
         this.speed = param4;
         MathUtil.saveINT(this._hp,param3);
      }
      
      public function get hp() : int
      {
         return MathUtil.loadINT(this._hp);
      }
   }
}

