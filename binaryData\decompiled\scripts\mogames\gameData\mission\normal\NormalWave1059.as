package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class NormalWave1059
   {
      
      public function NormalWave1059()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1059);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(414,67000,350,85,25,26,170,90,new BossSkillData0(100,{"hurt":550},1),0,1);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(111,1260,184,12,20,45,160,80,{
            "rate":100,
            "keepTime":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(111,1260,184,12,20,45,160,80,{
            "rate":100,
            "keepTime":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(102,1020,129,24,20,45,160,80,{
            "rate":100,
            "hurtBei":1.5,
            "atkPer":50,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(102,1020,129,24,20,45,160,80,{
            "rate":100,
            "hurtBei":1.5,
            "atkPer":50,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(102,1020,129,24,20,45,160,80,{
            "rate":100,
            "hurtBei":1.5,
            "atkPer":50,
            "keepTime":5
         })));
         _loc2_.addFu(new BossArgVO(205,18000,475,25,35,30,150,80,new BossSkillData1(8,{
            "hurt":560,
            "roleNum":20
         },2),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(101,1089,123,36,20,45,160,80,{
            "atkPer":60,
            "keepTime":10
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(101,1089,123,36,20,45,160,80,{
            "atkPer":60,
            "keepTime":10
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(104,1089,132,22,20,45,160,80,{
            "rate":200,
            "curePer":40
         })));
         _loc2_.addFu(new BossArgVO(203,18000,475,24,30,30,150,80,new BossSkillData1(10,{
            "hurt":360,
            "hurtCount":5
         },2),1003,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(104,1089,132,22,20,45,160,80,{
            "rate":200,
            "curePer":40
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(104,1089,132,22,20,45,160,80,{
            "rate":200,
            "curePer":40
         })));
         _loc2_.addFu(new BossArgVO(201,18000,475,24,30,30,150,80,new BossSkillData0(100,{
            "hurt":430,
            "hurtCount":5
         },2),1003,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

