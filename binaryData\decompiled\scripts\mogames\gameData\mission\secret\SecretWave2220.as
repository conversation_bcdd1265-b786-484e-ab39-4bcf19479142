package mogames.gameData.mission.secret
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class SecretWave2220
   {
      
      public function SecretWave2220()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2220);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(257,320000,4050,1020,50,100,200,120,new BossSkillData0(150,{"hurt":3850},3),1013,0);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(237,11000,1400,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(236,11000,1400,100,50,50,170,90,null)));
         _loc2_.addFu(new BossArgVO(292,100000,4000,200,30,30,116,50,new BossSkillData0(100,{
            "hurt":2750,
            "roleNum":20
         },2),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(238,11000,1400,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(238,11000,1400,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(237,11000,1400,100,50,50,170,90,null)));
         _loc2_.addFu(new BossArgVO(293,100000,4000,200,30,30,116,50,new BossSkillData0(100,{
            "hurt":2750,
            "roleNum":20
         },2),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(232,19500,5000,100,50,50,170,50,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(236,11000,1400,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(237,11000,1400,100,50,50,170,90,null)));
         _loc2_.addFu(new BossArgVO(292,100000,4000,200,30,30,116,50,new BossSkillData0(100,{"hurt":2700},2),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(12,new RoleArgVO(237,11000,1400,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(236,19500,5000,100,50,50,170,75,null)));
         _loc2_.addFu(new BossArgVO(293,100000,4000,200,30,30,116,50,new BossSkillData0(100,{
            "hurt":2750,
            "roleNum":20
         },2),1005,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

