package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class NormalWave2059
   {
      
      public function NormalWave2059()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2059);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(244,650000,3800,650,80,80,300,90,new BossSkillData0(150,{
            "hurt":4200,
            "roleNum":4
         },5),1004,0);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(248,10700,1240,100,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(249,10700,1240,100,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(248,10700,1240,100,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(250,10700,1240,100,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(248,10700,1240,100,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(241,120000,3600,450,50,80,150,80,new BossSkillData1(12,{
            "hurt":3850,
            "roleNum":20
         },2),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(248,10700,1240,100,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(247,10700,1240,100,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(248,10700,1240,100,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(249,10700,1240,100,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(14,new RoleArgVO(248,10700,1240,100,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(241,120000,3600,450,50,80,150,80,new BossSkillData0(150,{
            "hurt":3900,
            "roleNum":3
         },2),1016,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(248,10700,1240,100,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(250,10700,1240,100,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(248,10700,1240,100,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(241,120000,3600,450,50,80,150,80,new BossSkillData1(12,{
            "hurt":4053,
            "keepTime":8,
            "hurtCount":3
         },2),1015,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(248,10700,1240,100,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(249,10700,1240,100,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(248,10700,1240,100,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(241,120000,3600,450,50,80,150,80,new BossSkillData0(150,{"hurt":3710},2),1011,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

