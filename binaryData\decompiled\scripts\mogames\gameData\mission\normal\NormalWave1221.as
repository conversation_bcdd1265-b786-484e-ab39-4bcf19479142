package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class NormalWave1221
   {
      
      public function NormalWave1221()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1221);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(599,250000,2050,480,80,80,300,90,new BossSkillData0(150,{
            "hurt":3000,
            "roleNum":4
         },3),0,1);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(101,6350,770,100,50,50,200,120,{
            "atkPer":60,
            "keepTime":10
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(101,6350,770,100,50,50,200,120,{
            "atkPer":60,
            "keepTime":10
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(101,6350,770,100,50,50,200,120,{
            "atkPer":60,
            "keepTime":10
         })));
         _loc2_.addFu(new BossArgVO(202,71000,2800,350,50,80,150,80,new BossSkillData0(150,{
            "hurt":2355,
            "keepTime":3
         },1),1014,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(101,6350,770,100,50,50,200,120,{
            "atkPer":60,
            "keepTime":10
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(101,6350,770,100,50,50,200,120,{
            "atkPer":60,
            "keepTime":10
         })));
         _loc2_.addFu(new BossArgVO(203,71000,2800,350,50,80,150,80,new BossSkillData1(16,{
            "hurt":2250,
            "hurtCount":5
         },1),1002,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(101,6350,770,100,50,50,200,120,{
            "atkPer":60,
            "keepTime":10
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(101,6350,770,100,50,50,200,120,{
            "atkPer":60,
            "keepTime":10
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(101,6350,770,100,50,50,200,120,{
            "atkPer":60,
            "keepTime":10
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(101,6350,770,100,50,50,200,120,{
            "atkPer":60,
            "keepTime":10
         })));
         _loc2_.addFu(new BossArgVO(206,71000,2800,350,50,80,150,80,new BossSkillData0(150,{"hurt":6700},2),1006,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(101,6350,770,100,50,50,200,120,{
            "atkPer":60,
            "keepTime":10
         })));
         _loc2_.addFu(new BossArgVO(208,71000,2800,350,50,80,150,80,new BossSkillData0(150,{
            "hurt":2355,
            "keepTime":3
         },1),1014,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

