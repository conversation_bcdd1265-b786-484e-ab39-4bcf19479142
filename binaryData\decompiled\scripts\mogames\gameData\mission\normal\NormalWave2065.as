package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class NormalWave2065
   {
      
      public function NormalWave2065()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2065);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(246,675000,3900,650,80,80,300,90,new BossSkillData0(150,{"hurt":4700},5),1008,0);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(250,11300,1300,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(247,11300,1300,110,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(250,11300,1300,110,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(240,130000,3700,450,50,80,150,80,new BossSkillData1(12,{
            "hurt":4500,
            "hurtCount":5
         },1),1003,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(250,11300,1300,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(246,11300,1300,110,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(9,new RoleArgVO(250,11300,1300,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(9,new RoleArgVO(247,11300,1300,110,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(250,11300,1300,110,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(241,130000,3700,450,50,80,150,80,new BossSkillData0(150,{"hurt":4500},2),1011,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(250,11300,1300,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(248,11300,1300,110,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(250,11300,1300,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(249,11300,1300,110,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(250,11300,1300,110,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(242,100000,3700,450,50,80,150,80,new BossSkillData0(120,{
            "hurt":3500,
            "hurtCount":4
         },2),1017,0));
         _loc2_.addFu(new BossArgVO(241,100000,3700,450,50,30,150,80,new BossSkillData1(10,{"hurt":4000},2),1011,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(4,new RoleArgVO(250,11300,1300,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(4,new RoleArgVO(248,11300,1300,110,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(250,11300,1300,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(247,11300,1300,110,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

