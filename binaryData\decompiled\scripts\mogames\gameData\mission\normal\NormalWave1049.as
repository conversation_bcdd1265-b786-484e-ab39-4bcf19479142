package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class NormalWave1049
   {
      
      public function NormalWave1049()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1049);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(404,57000,260,46,25,36,170,90,new BossSkillData0(100,{
            "hurt":1250,
            "spdPer":80,
            "keepTime":5
         },1),0,1,12151);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(112,1221,94,16,20,20,190,80,{
            "rate":100,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(112,1221,94,16,20,20,190,80,{
            "rate":100,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(112,1221,94,16,20,20,190,80,{
            "rate":100,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(45);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(112,921,94,16,20,20,190,80,{
            "rate":100,
            "hurtBei":1.8
         })));
         _loc2_.addFu(new BossArgVO(212,17000,178,24,30,30,150,80,new BossSkillData0(80,{
            "hurt":300,
            "hurtCount":5
         },2),1003,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(112,1221,94,16,20,20,190,80,{
            "rate":100,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(112,1221,94,16,20,20,190,80,{
            "rate":100,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(112,1221,94,16,20,20,190,80,{
            "rate":100,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(112,1221,94,16,20,20,190,80,{
            "rate":100,
            "hurtBei":1.8
         })));
         _loc2_.addFu(new BossArgVO(212,15000,178,24,30,30,150,80,new BossSkillData0(50,{
            "hurt":290,
            "hurtCount":3
         },2),1017,0));
         _loc2_.addFu(new BossArgVO(212,10000,178,24,30,30,150,80,new BossSkillData0(50,{"hurt":300},2),1011,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(112,1221,94,16,20,20,190,80,{
            "rate":100,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(45);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(112,1221,94,16,20,20,190,80,{
            "rate":100,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

