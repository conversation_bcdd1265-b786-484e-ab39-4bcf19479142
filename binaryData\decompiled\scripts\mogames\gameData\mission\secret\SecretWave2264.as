package mogames.gameData.mission.secret
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class SecretWave2264
   {
      
      public function SecretWave2264()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2264);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(256,750000,5750,1050,50,100,200,120,new BossSkillData0(130,{"hurt":4370},5),1010,0);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(238,37000,3400,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(236,37000,3400,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(237,37000,3400,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(237,37000,3400,100,50,50,170,90,null)));
         _loc2_.addFu(new BossArgVO(293,250000,5150,200,30,30,150,50,new BossSkillData0(100,{
            "hurt":4750,
            "roleNum":20
         },2),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(45);
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(238,37000,3400,100,50,50,170,90,null)));
         _loc2_.addFu(new BossArgVO(290,250000,5150,200,30,30,150,50,new BossSkillData0(100,{
            "hurt":4750,
            "roleNum":20
         },2),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(237,37000,3400,100,50,50,170,90,null)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(232,50000,15000,100,50,50,170,50,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(236,37000,3400,100,50,50,170,55,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(232,50000,15000,100,50,50,170,50,null)));
         _loc2_.addFu(new BossArgVO(291,250000,5150,200,30,30,150,50,new BossSkillData0(100,{
            "hurt":4750,
            "roleNum":20
         },2),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(2,new RoleArgVO(236,37000,3400,100,50,50,170,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(2,new RoleArgVO(237,37000,3400,100,50,50,170,90,null)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(232,50000,15000,100,50,50,170,50,null)));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

