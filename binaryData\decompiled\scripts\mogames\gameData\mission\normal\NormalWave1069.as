package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class NormalWave1069
   {
      
      public function NormalWave1069()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1069);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(424,77000,450,66,95,36,170,90,new BossSkillData0(100,{"hurt":1000},1),0,1);
         _loc2_ = new OneWaveVO(45);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(112,1521,104,16,20,20,190,80,{
            "rate":100,
            "hurtBei":1.8
         })));
         _loc2_.addFu(new BossArgVO(212,17000,678,24,30,30,150,80,new BossSkillData0(80,{
            "hurt":500,
            "hurtCount":5
         },1),1003,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(112,1506,121,16,20,20,190,80,{
            "rate":100,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(112,1506,121,16,20,20,190,80,{
            "rate":100,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(112,1506,121,16,20,20,190,80,{
            "rate":100,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(112,1206,86,16,20,20,190,80,{
            "rate":100,
            "hurtBei":1.8
         })));
         _loc2_.addFu(new BossArgVO(212,17000,478,46,30,30,150,80,new BossSkillData0(60,{
            "hurt":490,
            "hurtCount":4
         },2),1017,0));
         _loc2_.addFu(new BossArgVO(212,17000,478,46,30,30,150,80,new BossSkillData0(60,{"hurt":500},2),1011,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(112,1506,121,16,20,20,190,80,{
            "rate":100,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(112,1506,121,16,20,20,190,80,{
            "rate":100,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(112,1506,121,16,20,20,190,80,{
            "rate":100,
            "hurtBei":1.8
         })));
         _loc2_.addFu(new BossArgVO(212,20000,678,46,30,30,150,80,new BossSkillData0(80,{"hurt":500},2),1011,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(112,1506,121,16,20,20,190,80,{
            "rate":100,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(112,1506,121,16,20,20,190,80,{
            "rate":100,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

