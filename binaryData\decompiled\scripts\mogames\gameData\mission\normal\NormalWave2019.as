package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class NormalWave2019
   {
      
      public function NormalWave2019()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2019);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(812,450000,3200,600,80,80,300,90,new <PERSON><PERSON>killData0(150,{
            "hurt":3000,
            "atkPer":5
         },5),1023,0);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(249,7900,960,130,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(247,7900,960,95,50,80,200,120,0)));
         _loc2_.addFu(new BossArgVO(241,91000,3400,400,50,80,150,80,new BossSkillData1(150,{
            "hurt":1153,
            "keepTime":8,
            "hurtCount":6
         },2),1015,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(248,7900,960,95,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(247,7900,960,95,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(248,7900,960,95,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(247,7900,960,95,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(12,new RoleArgVO(247,7900,960,95,50,80,200,120,0)));
         _loc2_.addFu(new BossArgVO(242,91000,3400,400,50,80,150,80,new BossSkillData1(8,{
            "hurt":1550,
            "keepTime":3
         },2),1007,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(250,7900,960,95,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(247,7900,960,95,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(247,7900,960,95,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(247,7900,960,95,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(247,7900,960,95,50,80,200,120,0)));
         _loc2_.addFu(new BossArgVO(240,91000,3400,400,50,80,150,80,new BossSkillData1(8,{
            "hurt":1650,
            "keepTime":3
         },2),1007,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(249,7900,960,130,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(247,7900,960,95,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(248,7900,960,95,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(247,7900,960,95,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(250,7900,960,95,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(247,7900,960,95,50,80,200,120,0)));
         _loc2_.addFu(new BossArgVO(242,91000,3400,400,50,80,150,80,new BossSkillData0(150,{
            "hurt":1560,
            "roleNum":4
         },2),1004,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

