package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class NormalWave1013
   {
      
      public function NormalWave1013()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1013);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(313,16000,81,0,20,20,160,70,new BossSkillData1(17,{"hurt":120},2),0,1);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(105,207,31,5,20,31,170,60,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(105,207,31,5,20,31,170,60,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(105,207,31,5,20,31,170,60,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(45);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(105,207,31,5,20,31,170,60,null)));
         _loc2_.addFu(new BossArgVO(201,6000,54,10,30,30,150,80,new BossSkillData0(30,{"hurt":120},1),1009,0));
         _loc2_.addFu(new BossArgVO(202,6000,54,12,30,30,150,80,new BossSkillData0(30,{"hurt":120},1),1013,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(105,207,31,5,20,31,170,60,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(105,207,31,5,20,31,170,60,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(105,207,31,5,20,31,170,60,null)));
         _loc2_.addFu(new BossArgVO(203,9000,54,10,30,30,150,80,new BossSkillData0(60,{
            "hurt":90,
            "keepTime":3
         },1),1007,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(45);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(105,207,31,5,20,31,170,60,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(105,207,31,5,20,31,170,60,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(60);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(105,207,31,5,20,31,170,60,null)));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

