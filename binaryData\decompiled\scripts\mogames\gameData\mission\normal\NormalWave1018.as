package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class NormalWave1018
   {
      
      public function NormalWave1018()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1018);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(318,26000,103,8,20,20,160,70,new BossSkillData0(50,{
            "hurt":90,
            "hurtCount":3
         },2),0,1);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(109,267,56,8,30,40,170,60,{
            "rate":100,
            "hurtPer":45,
            "spdPer":50,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(50);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(109,267,56,8,30,40,170,60,{
            "rate":100,
            "hurtPer":45,
            "spdPer":50,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(109,267,56,8,30,40,170,60,{
            "rate":100,
            "hurtPer":45,
            "spdPer":50,
            "keepTime":3
         })));
         _loc2_.addFu(new BossArgVO(202,8000,68,14,30,30,150,80,new BossSkillData0(60,{
            "hurt":100,
            "hurtCount":3
         },1),1019,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(109,267,56,8,30,40,170,60,{
            "rate":100,
            "hurtPer":45,
            "spdPer":50,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(109,267,56,8,30,40,170,60,{
            "rate":50,
            "hurtPer":45,
            "spdPer":50,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(109,267,56,8,30,40,170,60,{
            "rate":50,
            "hurtPer":45,
            "spdPer":50,
            "keepTime":3
         })));
         _loc2_.addFu(new BossArgVO(203,8000,68,14,30,30,150,80,new BossSkillData0(60,{
            "hurt":120,
            "roleNum":2
         },1),1016,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(109,267,56,8,30,40,170,60,{
            "rate":50,
            "hurtPer":45,
            "spdPer":50,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(109,267,56,8,30,40,170,60,{
            "rate":50,
            "hurtPer":45,
            "spdPer":50,
            "keepTime":3
         })));
         _loc2_.addFu(new BossArgVO(201,8000,68,14,30,30,150,80,new BossSkillData1(10,{"hurt":130},1),1010,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(109,267,56,8,30,40,170,60,{
            "rate":50,
            "hurtPer":45,
            "spdPer":50,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(25,new RoleArgVO(109,267,56,8,30,40,170,60,{
            "rate":50,
            "hurtPer":45,
            "spdPer":50,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

