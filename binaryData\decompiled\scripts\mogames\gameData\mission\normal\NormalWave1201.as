package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class NormalWave1201
   {
      
      public function NormalWave1201()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1201);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(576,140000,1450,266,80,80,300,90,new BossSkillData0(150,{
            "hurt":3000,
            "roleNum":4
         },2),0,1);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(107,4700,505,80,50,50,200,120,{
            "rate":150,
            "hurtBei":1.5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(106,4700,505,80,50,50,200,120,{"atkPer":20})));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(107,4700,505,80,50,50,200,120,{
            "rate":150,
            "hurtBei":1.5
         })));
         _loc2_.addFu(new BossArgVO(202,51000,2000,240,50,80,150,80,new BossSkillData0(150,{
            "hurt":2355,
            "keepTime":3
         },1),1014,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(102,4700,505,80,50,50,200,120,{
            "rate":100,
            "hurtBei":1.6,
            "atkPer":60,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(107,4700,505,80,50,50,200,120,{
            "rate":150,
            "hurtBei":1.5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(105,4700,505,80,50,50,200,120,{
            "rate":100,
            "curePer":30
         })));
         _loc2_.addFu(new BossArgVO(203,51000,2000,240,50,80,150,80,new BossSkillData0(150,{
            "hurt":2250,
            "hurtCount":5
         },1),1002,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(102,4700,505,80,50,50,200,120,{
            "rate":100,
            "hurtBei":1.6,
            "atkPer":60,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(105,4700,505,80,50,50,200,120,{
            "rate":100,
            "curePer":30
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(106,4700,505,80,50,50,200,120,{"atkPer":20})));
         _loc2_.addFu(new BossArgVO(206,51000,2000,240,50,80,150,80,new BossSkillData0(150,{"hurt":6700},2),1006,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(108,3900,600,80,50,50,200,120,{
            "rate":100,
            "hurtPer":20,
            "keepTime":5
         })));
         _loc2_.addFu(new BossArgVO(208,51000,2000,240,50,80,150,80,new BossSkillData0(150,{
            "hurt":2355,
            "keepTime":3
         },1),1014,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

