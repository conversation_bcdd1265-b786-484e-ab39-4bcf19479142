package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class NormalWave2052
   {
      
      public function NormalWave2052()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2052);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(243,600000,3700,650,80,80,300,90,new BossSkillData0(150,{
            "hurt":4700,
            "keepTime":3
         },5),1014,0);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(248,10000,1170,120,50,80,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(248,10000,1170,120,50,80,200,120,0)));
         _loc2_.addFu(new BossArgVO(242,115000,3600,450,50,80,150,80,new BossSkillData0(150,{
            "hurt":3000,
            "keepTime":3
         },2),1014,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(248,10000,1170,120,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(247,10000,1170,120,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(248,10000,1170,120,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(247,10000,1170,120,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(242,115000,3600,450,50,80,150,80,new BossSkillData1(10,{
            "hurt":3000,
            "keepTime":3
         },2),1007,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(248,10000,1170,120,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(247,10000,1170,120,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(248,10000,1170,120,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(247,10000,1170,120,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(242,115000,3600,450,50,80,150,80,new BossSkillData1(10,{"hurt":3000},2),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(248,10000,1170,120,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(247,10000,1170,120,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(248,10000,1170,120,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(247,10000,1170,120,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(242,115000,3600,450,50,80,150,80,new BossSkillData0(150,{"hurt":3000},2),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(248,10000,1170,120,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(247,10000,1170,120,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(248,10000,1170,120,50,80,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(247,10000,1170,120,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

