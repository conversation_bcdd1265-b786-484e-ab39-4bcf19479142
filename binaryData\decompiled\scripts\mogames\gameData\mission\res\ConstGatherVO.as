package mogames.gameData.mission.res
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   
   public class ConstGatherVO extends ConstResVO
   {
      
      private var _num:Oint = new Oint();
      
      public function ConstGatherVO(param1:int, param2:int, param3:String)
      {
         super(param1,param3);
         MathUtil.saveINT(this._num,param2);
      }
      
      public function get num() : int
      {
         return MathUtil.loadINT(this._num);
      }
   }
}

