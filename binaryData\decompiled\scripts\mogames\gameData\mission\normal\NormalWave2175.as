package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class NormalWave2175
   {
      
      public function NormalWave2175()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2175);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(824,1600000,7200,800,80,80,300,90,new BossSkillData0(150,{
            "hurt":6400,
            "roleNum":3
         },5),1026,0);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(272,31000,2780,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(4,new RoleArgVO(275,31000,2780,110,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(272,31000,2780,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(275,31000,2780,110,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(272,31000,2780,110,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(276,450000,4900,450,50,80,150,80,new BossSkillData1(12,{"hurt":20000},2),1006,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(272,31000,2780,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(275,31000,2780,110,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(272,31000,2780,110,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(276,450000,4900,450,50,80,150,80,new BossSkillData1(12,{"hurt":20000},2),1006,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(272,31000,2780,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(275,31000,2780,110,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(272,31000,2780,110,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(276,450000,4900,450,50,80,150,80,new BossSkillData0(250,{"hurt":20000},2),1006,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(272,31000,2780,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(275,31000,2780,110,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(272,31000,2780,110,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(276,450000,4900,450,50,80,150,80,new BossSkillData0(250,{
            "hurt":6550,
            "hurtCount":5
         },2),1002,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(272,31000,2780,110,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(275,31000,2780,110,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

