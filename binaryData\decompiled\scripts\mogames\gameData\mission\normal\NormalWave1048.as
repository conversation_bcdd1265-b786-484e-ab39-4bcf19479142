package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class NormalWave1048
   {
      
      public function NormalWave1048()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1048);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(403,56000,250,64,25,35,170,90,new BossSkillData0(100,{"hurt":380},1),0,1);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(111,1060,174,12,20,25,160,80,{
            "rate":100,
            "keepTime":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(111,1060,174,12,20,25,160,80,{
            "rate":100,
            "keepTime":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(45);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(105,1073,113,24,20,25,160,80,{
            "rate":150,
            "curePer":100
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(105,1273,113,24,20,25,160,80,{
            "rate":150,
            "curePer":100
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(105,1273,113,24,20,25,160,80,{
            "rate":150,
            "curePer":100
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(107,967,116,21,20,25,160,80,{
            "rate":100,
            "hurtBei":1.5
         })));
         _loc2_.addFu(new BossArgVO(206,15000,368,35,30,30,150,80,new BossSkillData0(50,{
            "hurt":250,
            "keepTime":3
         },2),1014,0));
         _loc2_.addFu(new BossArgVO(206,15000,368,35,30,30,150,80,new BossSkillData0(40,{
            "hurt":350,
            "keepTime":3
         },2),1007,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(107,1267,116,21,20,25,160,80,{
            "rate":150,
            "hurtBei":1.5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(109,1267,113,24,20,25,160,80,{
            "rate":100,
            "hurtPer":60,
            "spdPer":50,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(109,967,113,24,20,25,160,80,{
            "rate":100,
            "hurtPer":60,
            "spdPer":50,
            "keepTime":3
         })));
         _loc2_.addFu(new BossArgVO(205,13000,478,30,30,30,150,80,new BossSkillData0(80,{"hurt":400}),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(101,1289,113,36,20,25,160,80,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc2_.addFu(new BossArgVO(204,12000,478,30,30,30,150,80,new BossSkillData0(80,{"hurt":400},2),1008,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

