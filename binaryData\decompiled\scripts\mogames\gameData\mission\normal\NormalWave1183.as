package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class NormalWave1183
   {
      
      public function NormalWave1183()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1183);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(556,108000,1250,230,25,37,170,90,new BossSkillData0(100,{"hurt":2800},1),0,1);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(107,4500,470,60,20,50,190,80,{
            "rate":150,
            "hurtBei":1.5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(107,4500,470,60,20,50,190,80,{
            "rate":150,
            "hurtBei":1.5
         })));
         _loc2_.addFu(new BossArgVO(202,44000,1600,200,30,30,150,80,new BossSkillData0(100,{
            "hurt":1055,
            "keepTime":3
         },1),1014,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(107,4500,470,60,20,50,190,80,{
            "rate":150,
            "hurtBei":1.5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(102,4500,470,60,20,50,190,80,{
            "rate":100,
            "hurtBei":1.6,
            "atkPer":60,
            "keepTime":5
         })));
         _loc2_.addFu(new BossArgVO(201,44000,1600,200,30,30,150,80,new BossSkillData0(100,{
            "hurt":1050,
            "hurtCount":5
         },2),1002,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(102,4500,470,60,20,50,190,80,{
            "rate":100,
            "hurtBei":1.6,
            "atkPer":60,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(105,4500,470,60,20,50,190,80,{
            "rate":100,
            "curePer":30
         })));
         _loc2_.addFu(new BossArgVO(201,44000,1600,200,30,30,150,80,new BossSkillData0(100,{
            "hurt":1150,
            "hurtCount":5
         },2),1002,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(105,4500,470,60,20,50,190,80,{
            "rate":100,
            "curePer":30
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(106,4500,470,60,20,50,190,80,{"atkPer":30})));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(106,4500,470,60,20,50,190,80,{"atkPer":30})));
         _loc2_.addFu(new BossArgVO(202,44000,1600,200,30,30,150,80,new BossSkillData0(100,{"hurt":1300},2),1010,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(108,3000,470,45,20,50,190,80,{
            "rate":100,
            "hurtPer":20,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

