package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class NormalWave1053
   {
      
      public function NormalWave1053()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1053);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(408,61000,300,54,25,40,170,90,new BossSkillData0(80,{"hurt":485},1),0,1);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(2,new RoleArgVO(108,1560,70,12,20,40,170,80,{
            "rate":120,
            "hurtPer":30,
            "keepTime":3
         })));
         _loc2_.addFu(new BossArgVO(201,10000,354,40,30,30,150,80,new BossSkillData0(60,{
            "hurt":253,
            "keepTime":5,
            "hurtCount":3
         }),1001,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(2,new RoleArgVO(108,1560,70,12,20,40,170,80,{
            "rate":120,
            "hurtPer":30,
            "keepTime":3
         })));
         _loc2_.addFu(new BossArgVO(202,10000,354,40,30,30,150,80,new BossSkillData0(60,{
            "hurt":450,
            "hurtCount":5
         }),1002,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(2,new RoleArgVO(108,1560,70,12,20,40,170,80,{
            "rate":120,
            "hurtPer":30,
            "keepTime":3
         })));
         _loc2_.addFu(new BossArgVO(203,10000,354,40,30,30,150,80,new BossSkillData0(60,{
            "hurt":450,
            "hurtCount":5
         }),1003,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(2,new RoleArgVO(105,1573,113,24,20,25,160,80,{
            "rate":150,
            "curePer":100
         })));
         _loc2_.addFu(new BossArgVO(204,10000,354,40,30,30,150,80,new BossSkillData0(60,{
            "hurt":460,
            "roleNum":4
         }),1004,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(2,new RoleArgVO(105,1573,113,24,20,25,160,80,{
            "rate":150,
            "curePer":100
         })));
         _loc2_.addFu(new BossArgVO(205,10000,354,40,30,30,150,80,new BossSkillData0(60,{
            "hurt":460,
            "roleNum":20
         }),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(2,new RoleArgVO(101,1589,113,36,25,30,160,80,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc2_.addFu(new BossArgVO(206,10000,354,40,30,30,150,80,new BossSkillData0(60,{"hurt":490}),1006,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(2,new RoleArgVO(101,1589,113,36,25,30,160,80,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc2_.addFu(new BossArgVO(207,10000,354,40,30,30,150,80,new BossSkillData0(60,{"hurt":490}),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(2,new RoleArgVO(105,1573,113,24,20,25,160,80,{
            "rate":150,
            "curePer":100
         })));
         _loc2_.addFu(new BossArgVO(208,10000,224,20,30,30,150,80,new BossSkillData0(50,{"hurt":460}),1010,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(2,new RoleArgVO(101,1589,113,36,25,30,160,80,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc2_.addFu(new BossArgVO(209,10000,354,40,30,30,150,80,new BossSkillData0(60,{"hurt":460}),1013,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(2,new RoleArgVO(108,1560,80,12,20,40,170,80,{
            "rate":120,
            "hurtPer":30,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

