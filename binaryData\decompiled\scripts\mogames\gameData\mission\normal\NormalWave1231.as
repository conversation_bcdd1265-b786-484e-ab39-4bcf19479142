package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class NormalWave1231
   {
      
      public function NormalWave1231()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1231);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(609,300000,2350,550,80,80,300,90,new BossSkillData0(150,{
            "hurt":3300,
            "keepTime":3
         },3),0,1);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(111,5000,860,120,50,80,200,120,{
            "rate":100,
            "keepTime":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(105,6850,860,120,50,80,200,120,{
            "rate":150,
            "curePer":100
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(105,6850,860,120,50,80,200,120,{
            "rate":150,
            "curePer":100
         })));
         _loc2_.addFu(new BossArgVO(212,76000,3000,380,50,80,150,80,new BossSkillData0(150,{
            "hurt":1600,
            "keepTime":3
         },2),1014,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(111,5000,860,120,50,80,200,120,{
            "rate":100,
            "keepTime":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(25,new RoleArgVO(107,6850,860,120,50,80,200,120,{
            "rate":100,
            "hurtBei":1.5
         })));
         _loc2_.addFu(new BossArgVO(205,76000,3000,380,50,80,150,80,new BossSkillData1(10,{
            "hurt":1650,
            "keepTime":3
         },2),1007,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(107,6850,860,120,50,80,200,120,{
            "rate":150,
            "hurtBei":1.5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(109,6850,860,120,50,80,200,120,{
            "rate":100,
            "hurtPer":60,
            "spdPer":50,
            "keepTime":3
         })));
         _loc2_.addFu(new BossArgVO(201,41000,2400,320,50,80,150,80,new BossSkillData1(10,{"hurt":1600},2),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(109,6850,860,120,50,80,200,120,{
            "rate":100,
            "hurtPer":60,
            "spdPer":50,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(105,6850,860,120,50,80,200,120,{
            "rate":150,
            "curePer":100
         })));
         _loc2_.addFu(new BossArgVO(209,41000,2400,320,50,80,150,80,new BossSkillData0(150,{"hurt":1600},2),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(18,new RoleArgVO(101,6850,860,120,50,80,200,120,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

