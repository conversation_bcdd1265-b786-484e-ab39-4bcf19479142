package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class NormalWave1155
   {
      
      public function NormalWave1155()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1155);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(522,102000,1110,214,25,31,170,90,new BossSkillData0(100,{
            "hurt":2105,
            "keepTime":3
         },1),0,1);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(108,2600,255,19,20,40,170,80,{
            "rate":120,
            "hurtPer":30,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(108,2600,255,19,20,40,170,80,{
            "rate":120,
            "hurtPer":30,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(25,new RoleArgVO(101,3700,330,58,25,30,160,80,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(105,3700,330,38,20,25,160,80,{
            "rate":150,
            "curePer":100
         })));
         _loc2_.addFu(new BossArgVO(202,39000,1100,170,30,30,150,80,new BossSkillData0(100,{"hurt":1100},2),1009,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(105,3700,330,38,20,25,160,80,{
            "rate":150,
            "curePer":100
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(101,3700,330,58,25,30,160,80,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(108,2600,255,19,20,40,170,80,{
            "rate":120,
            "hurtPer":30,
            "keepTime":3
         })));
         _loc2_.addFu(new BossArgVO(203,39000,1100,170,30,30,150,80,new BossSkillData0(100,{
            "hurt":790,
            "keepTime":3
         },2),1007,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(25,new RoleArgVO(105,3700,330,38,20,25,160,80,{
            "rate":150,
            "curePer":100
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(25,new RoleArgVO(101,3700,330,58,25,30,160,80,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc2_.addFu(new BossArgVO(202,39000,1100,170,30,30,150,80,new BossSkillData0(100,{"hurt":900},2),1013,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(108,2600,245,13,20,40,170,80,{
            "rate":120,
            "hurtPer":30,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

