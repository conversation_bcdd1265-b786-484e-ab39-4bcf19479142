package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class NormalWave1019
   {
      
      public function NormalWave1019()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1019);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(319,27000,108,8,20,20,160,70,new BossSkillData1(14,{
            "hurt":250,
            "keepTime":8
         },2),0,0,12021);
         _loc2_ = new OneWaveVO(60);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(106,379,58,7,25,40,170,60,{"atkPer":20})));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(106,379,58,7,25,40,170,60,{"atkPer":20})));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(106,379,58,7,25,40,170,60,{"atkPer":20})));
         _loc2_.addFu(new BossArgVO(201,8000,78,15,30,30,150,80,new BossSkillData0(60,{"hurt":150},1),1006,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(106,379,58,7,25,40,170,60,{"atkPer":20})));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(106,379,58,7,25,40,170,60,{"atkPer":20})));
         _loc2_.addFu(new BossArgVO(201,8000,78,15,30,30,150,80,new BossSkillData0(60,{
            "hurt":120,
            "hurtCount":5
         },1),1003,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(106,379,58,7,25,40,170,60,{"atkPer":20})));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(106,379,58,7,25,40,170,60,{"atkPer":20})));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(106,379,58,7,25,40,170,60,{"atkPer":20})));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(106,379,58,7,25,40,170,60,{"atkPer":20})));
         _loc2_.addFu(new BossArgVO(202,8000,75,15,30,30,150,80,new BossSkillData0(60,{
            "hurt":60,
            "keepTime":8,
            "hurtCount":2
         },1),1015,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(106,379,58,7,25,40,170,60,{"atkPer":20})));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

