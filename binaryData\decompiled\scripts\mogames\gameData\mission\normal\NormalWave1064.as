package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class NormalWave1064
   {
      
      public function NormalWave1064()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1064);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(419,72000,400,90,25,31,170,90,new BossSkillData0(80,{"hurt":810},1),0,1);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(108,1400,89,13,20,40,170,80,{
            "rate":120,
            "hurtPer":30,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(108,1400,89,13,20,40,170,80,{
            "rate":120,
            "hurtPer":30,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(101,1476,135,40,25,30,160,80,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(105,1478,135,26,20,25,160,80,{
            "rate":150,
            "curePer":100
         })));
         _loc2_.addFu(new BossArgVO(202,17000,584,42,30,30,150,80,new BossSkillData0(60,{"hurt":600},2),1009,0));
         _loc2_.addFu(new BossArgVO(203,17000,584,42,30,30,150,80,new BossSkillData0(80,{
            "hurt":390,
            "keepTime":3
         },2),1007,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(105,1478,135,26,20,25,160,80,{
            "rate":150,
            "curePer":100
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(101,1476,135,40,25,30,160,80,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(108,1400,89,13,20,40,170,80,{
            "rate":120,
            "hurtPer":30,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(105,1478,135,26,20,25,160,80,{
            "rate":150,
            "curePer":100
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(101,1476,135,40,25,30,160,80,{
            "atkPer":50,
            "keepTime":20
         })));
         _loc2_.addFu(new BossArgVO(202,20000,604,42,30,30,150,80,new BossSkillData0(100,{"hurt":500},2),1013,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(108,1800,89,13,20,40,170,80,{
            "rate":120,
            "hurtPer":30,
            "keepTime":3
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

