package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class NormalWave1012
   {
      
      public function NormalWave1012()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1012);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(312,20000,89,0,20,20,160,70,new BossSkillData0(30,{
            "hurt":110,
            "killPer":10
         },1),0,0,12001);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new <PERSON>ArgVO(111,145,58,3,40,80,200,60,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(111,145,58,3,40,80,200,60,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(111,145,58,3,40,80,200,60,{
            "rate":60,
            "keepTime":2
         })));
         _loc2_.addFu(new BossArgVO(201,6000,65,1,30,30,150,80,new BossSkillData0(60,{"hurt":80},1),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(111,145,58,3,40,80,200,60,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(111,145,58,3,40,80,200,60,null)));
         _loc2_.addFu(new BossArgVO(202,6000,65,1,30,30,150,80,new BossSkillData0(60,{"hurt":80},1),1018,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(111,145,58,3,40,80,200,60,{
            "rate":60,
            "keepTime":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(111,145,58,3,40,80,200,60,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(50);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(111,145,58,3,40,80,200,60,{
            "rate":60,
            "keepTime":2
         })));
         _loc2_.addFu(new BossArgVO(203,8000,65,1,30,30,150,80,new BossSkillData0(60,{"hurt":80},1),1010,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(55);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(111,145,58,3,40,80,200,60,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(111,145,58,3,40,80,200,60,{
            "rate":60,
            "keepTime":2
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

