package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class NormalWave1139
   {
      
      public function NormalWave1139()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1139);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(506,99000,1030,182,25,44,170,90,new BossSkillData0(100,{
            "hurt":2520,
            "keepTime":3
         },1),0,1);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(106,3000,265,29,25,40,180,80,{"atkPer":15})));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(106,3000,265,29,25,40,180,80,{"atkPer":15})));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(111,2350,300,19,25,40,180,80,{
            "rate":150,
            "keepTime":2
         })));
         _loc2_.addFu(new BossArgVO(203,32000,970,140,30,30,150,80,new BossSkillData0(100,{
            "hurt":860,
            "roleNum":3
         },2),1016,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(111,2350,300,19,25,40,180,80,{
            "rate":150,
            "keepTime":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(111,2350,300,19,25,40,180,80,{
            "rate":150,
            "keepTime":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(45);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(102,3000,265,28,25,40,180,80,{
            "rate":150,
            "hurtBei":1.6,
            "atkPer":50,
            "keepTime":5
         })));
         _loc2_.addFu(new BossArgVO(210,32000,970,140,30,30,150,80,new BossSkillData1(10,{
            "hurt":593,
            "keepTime":8,
            "hurtCount":3
         },2),1015,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(102,3000,265,28,25,40,180,80,{
            "rate":150,
            "hurtBei":1.6,
            "atkPer":50,
            "keepTime":5
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(12,new RoleArgVO(105,3000,265,28,25,40,180,80,{
            "rate":150,
            "curePer":100
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(25,new RoleArgVO(105,3000,265,28,25,40,180,80,{
            "rate":150,
            "curePer":100
         })));
         _loc2_.addFu(new BossArgVO(202,32000,970,140,30,30,150,80,new BossSkillData0(100,{
            "hurt":900,
            "roleNum":20
         },2),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(45);
         _loc2_.addEnemy(new WaveEnemyVO(25,new RoleArgVO(111,2350,300,19,25,40,180,80,{
            "rate":150,
            "keepTime":2
         })));
         _loc2_.addFu(new BossArgVO(203,32000,970,140,30,30,150,80,new BossSkillData0(100,{"hurt":710},2),1011,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

