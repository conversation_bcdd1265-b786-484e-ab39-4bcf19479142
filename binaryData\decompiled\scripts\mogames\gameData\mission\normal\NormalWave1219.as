package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class NormalWave1219
   {
      
      public function NormalWave1219()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1219);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(597,250000,1990,440,80,80,300,90,new BossSkillData0(150,{
            "hurt":3500,
            "hurtCount":5
         },3),0,1);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(104,6250,750,100,50,50,200,120,{
            "rate":200,
            "curePer":50
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(104,6250,750,100,50,50,200,120,{
            "rate":200,
            "curePer":50
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(104,6250,750,100,50,50,200,120,{
            "rate":200,
            "curePer":50
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(104,6250,750,100,50,50,200,120,{
            "rate":200,
            "curePer":50
         })));
         _loc2_.addFu(new BossArgVO(205,71000,2800,350,50,80,150,80,new BossSkillData1(16,{
            "hurt":2353,
            "keepTime":8,
            "hurtCount":3
         },2),1015,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(104,6250,750,100,50,50,200,120,{
            "rate":200,
            "curePer":50
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(25,new RoleArgVO(104,6250,750,100,50,50,200,120,{
            "rate":200,
            "curePer":50
         })));
         _loc2_.addFu(new BossArgVO(206,71000,2800,350,50,80,150,80,new BossSkillData1(16,{
            "hurt":2150,
            "roleNum":20
         },2),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(12,new RoleArgVO(104,6250,750,100,50,50,200,120,{
            "rate":200,
            "curePer":50
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(104,6250,750,100,50,50,200,120,{
            "rate":200,
            "curePer":50
         })));
         _loc2_.addFu(new BossArgVO(207,71000,2800,350,50,80,150,80,new BossSkillData0(150,{
            "hurt":2100,
            "roleNum":3
         },2),1016,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(104,6250,750,100,50,50,200,120,{
            "rate":200,
            "curePer":50
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(25,new RoleArgVO(104,6250,750,100,50,50,200,120,{
            "rate":200,
            "curePer":50
         })));
         _loc2_.addFu(new BossArgVO(208,71000,2800,350,50,80,150,80,new BossSkillData0(150,{"hurt":1710},2),1011,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

