package mogames.gameData.mission.reward
{
   import com.mogames.data.Oint;
   import com.mogames.utils.MathUtil;
   import mogames.gameData.mission.MissionProxy;
   
   public class AreaRewardVO
   {
      
      private var _areaID:Oint = new Oint();
      
      private var _list:Array;
      
      public function AreaRewardVO(param1:int, param2:Array)
      {
         super();
         MathUtil.saveINT(this._areaID,param1);
         this._list = param2;
      }
      
      public function get areaID() : int
      {
         return MathUtil.loadINT(this._areaID);
      }
      
      public function get boxes() : Array
      {
         return this._list;
      }
      
      public function get curStar() : int
      {
         return MissionProxy.instance().curStar(this.areaID);
      }
      
      public function get totalStar() : int
      {
         return MissionProxy.instance().totalStar(this.areaID);
      }
      
      public function get maxRewardStar() : int
      {
         return this._list[this._list.length - 1].needStar;
      }
   }
}

