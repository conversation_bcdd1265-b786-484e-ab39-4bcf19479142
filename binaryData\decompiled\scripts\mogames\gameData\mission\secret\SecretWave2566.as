package mogames.gameData.mission.secret
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class SecretWave2566
   {
      
      public function SecretWave2566()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2566);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(744,120000000,500000,500000,50,100,200,120,new BossSkillData0(150,{"hurt":700000},99),1011,0);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(740,3000000,220000,190000,150,150,270,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(740,3000000,220000,190000,150,150,270,90,null)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(232,4000000,1000000,500000,150,150,270,50,null)));
         _loc2_.addFu(new BossArgVO(742,14000000,400000,400000,100,200,250,50,new BossSkillData0(150,{
            "hurt":350000,
            "roleNum":20
         },3),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(740,3000000,220000,190000,150,150,270,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(5);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(740,3000000,220000,190000,150,150,270,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(740,3000000,220000,190000,150,150,270,90,null)));
         _loc2_.addFu(new BossArgVO(742,14000000,400000,400000,100,200,250,50,new BossSkillData0(150,{
            "hurt":350000,
            "roleNum":20
         },3),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(740,3000000,220000,190000,150,150,270,50,null)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(740,3000000,220000,190000,150,150,270,50,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(6,new RoleArgVO(740,3000000,220000,190000,150,150,270,90,null)));
         _loc2_.addFu(new BossArgVO(742,14000000,400000,400000,100,200,250,50,new BossSkillData0(150,{
            "hurt":350000,
            "roleNum":20
         },3),1005,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(740,2750000,100000,100,150,150,270,50,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(740,3000000,220000,190000,150,150,270,90,null)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(740,3000000,220000,190000,150,150,270,90,null)));
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(232,5000000,1200000,500000,150,150,270,50,null)));
         _loc2_.addFu(new BossArgVO(742,14000000,400000,400000,100,200,250,50,new BossSkillData0(150,{
            "hurt":350000,
            "roleNum":20
         },3),1005,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

