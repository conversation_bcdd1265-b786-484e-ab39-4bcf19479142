package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class NormalWave1245
   {
      
      public function NormalWave1245()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1245);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(630,350000,2650,550,80,80,300,90,new BossSkillData0(150,{"hurt":3100},3),0,1);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(108,5200,920,110,50,50,200,120,{
            "rate":150,
            "hurtPer":20,
            "keepTime":3
         })));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(111,7200,920,110,50,50,200,120,{
            "rate":90,
            "keepTime":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(3,new RoleArgVO(108,5200,920,110,50,50,200,120,{
            "rate":150,
            "hurtPer":20,
            "keepTime":3
         })));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(111,7200,920,110,50,50,200,120,{
            "rate":90,
            "keepTime":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(108,5200,920,110,50,50,200,120,{
            "rate":150,
            "hurtPer":20,
            "keepTime":3
         })));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(111,7200,920,110,50,50,200,120,{
            "rate":90,
            "keepTime":2
         })));
         _loc2_.addFu(new BossArgVO(212,81000,3000,400,50,80,150,80,new BossSkillData1(12,{
            "hurt":2600,
            "hurtCount":5
         },1),1003,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(108,5200,920,110,50,50,200,120,{
            "rate":150,
            "hurtPer":20,
            "keepTime":3
         })));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(111,7200,920,110,50,50,200,120,{
            "rate":90,
            "keepTime":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(12,new RoleArgVO(108,5200,920,110,50,50,200,120,{
            "rate":150,
            "hurtPer":20,
            "keepTime":3
         })));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(111,7200,920,110,50,50,200,120,{
            "rate":90,
            "keepTime":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(108,5200,920,110,50,50,200,120,{
            "rate":150,
            "hurtPer":20,
            "keepTime":3
         })));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(111,7200,920,110,50,50,200,120,{
            "rate":90,
            "keepTime":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(108,5200,920,110,50,50,200,120,{
            "rate":150,
            "hurtPer":20,
            "keepTime":3
         })));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(111,7200,920,110,50,50,200,120,{
            "rate":90,
            "keepTime":2
         })));
         _loc2_.addFu(new BossArgVO(203,81000,3000,400,50,80,150,80,new BossSkillData0(150,{"hurt":2600},2),1011,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(108,5200,920,110,50,50,200,120,{
            "rate":150,
            "hurtPer":20,
            "keepTime":3
         })));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(111,7200,920,110,50,50,200,120,{
            "rate":90,
            "keepTime":2
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(108,5200,920,110,50,50,200,120,{
            "rate":150,
            "hurtPer":20,
            "keepTime":3
         })));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(111,7200,920,110,50,50,200,120,{
            "rate":90,
            "keepTime":2
         })));
         _loc2_.addFu(new BossArgVO(208,46000,2600,350,50,80,150,80,new BossSkillData0(80,{
            "hurt":1690,
            "hurtCount":4
         },2),1017,0));
         _loc2_.addFu(new BossArgVO(209,46000,2600,350,50,30,150,80,new BossSkillData1(10,{"hurt":1600},2),1011,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(35);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(108,5200,920,110,50,50,200,120,{
            "rate":150,
            "hurtPer":20,
            "keepTime":3
         })));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(111,7200,920,110,50,50,200,120,{
            "rate":90,
            "keepTime":2
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

