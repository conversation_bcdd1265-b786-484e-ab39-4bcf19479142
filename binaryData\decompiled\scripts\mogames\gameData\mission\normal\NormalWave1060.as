package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class NormalWave1060
   {
      
      public function NormalWave1060()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1060);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(415,68000,360,86,25,27,170,90,new BossSkillData0(80,{
            "hurt":460,
            "roleNum":4
         },1),0,1);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(105,1308,127,25,40,50,170,80,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(105,1308,127,25,40,50,170,80,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(105,1308,127,25,40,50,170,80,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc2_.addFu(new BossArgVO(212,18000,475,32,30,30,150,80,new BossSkillData0(80,{
            "hurt":350,
            "hurtCount":5
         },2),1002,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(105,1308,127,25,40,50,170,80,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(105,1308,127,25,40,50,170,80,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(105,1308,127,25,40,50,170,80,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc2_.addFu(new BossArgVO(212,18000,475,32,30,30,150,80,new BossSkillData0(80,{"hurt":800},2),1006,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(105,1308,127,25,40,50,170,80,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(105,1308,127,25,40,50,170,80,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc2_.addFu(new BossArgVO(212,18000,475,32,30,30,150,80,new BossSkillData0(80,{"hurt":1000},2),1006,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(105,1308,127,25,40,50,170,80,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(105,1308,127,25,40,50,170,80,{
            "atkPer":60,
            "keepTime":20
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

