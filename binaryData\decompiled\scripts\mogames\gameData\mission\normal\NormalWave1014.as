package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   
   public class NormalWave1014
   {
      
      public function NormalWave1014()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(1014);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(351,18000,94,0,20,20,160,70,new BossSkillData0(50,{
            "hurt":83,
            "keepTime":5,
            "hurtCount":3
         },2),0,1);
         _loc2_ = new OneWaveVO(70);
         _loc2_.addEnemy(new WaveEnemyVO(20,new RoleArgVO(112,147,41,4,25,40,180,60,{
            "rate":50,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(112,147,41,4,25,40,180,60,{
            "rate":50,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(112,147,41,4,25,40,180,60,{
            "rate":50,
            "hurtBei":1.8
         })));
         _loc2_.addFu(new BossArgVO(202,7000,55,13,30,30,150,80,new BossSkillData0(40,{
            "hurt":100,
            "roleNum":3
         },1),1016,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(112,147,41,4,25,40,180,60,{
            "rate":50,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(112,147,41,4,25,40,180,60,{
            "rate":50,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(112,147,41,4,25,40,180,60,{
            "rate":50,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(112,147,49,4,25,40,180,60,{
            "rate":50,
            "hurtBei":1.8
         })));
         _loc2_.addFu(new BossArgVO(201,5000,65,13,30,30,150,80,new BossSkillData0(30,{
            "hurt":90,
            "roleNum":10
         },1),1005,0));
         _loc2_.addFu(new BossArgVO(203,5000,55,13,30,30,150,80,new BossSkillData0(30,{"hurt":90},1),1011,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(112,147,41,4,25,40,180,60,{
            "rate":50,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(112,147,41,4,25,40,180,60,{
            "rate":50,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(40);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(112,147,41,4,25,40,180,60,{
            "rate":50,
            "hurtBei":1.8
         })));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

