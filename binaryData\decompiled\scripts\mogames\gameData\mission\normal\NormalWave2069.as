package mogames.gameData.mission.normal
{
   import mogames.gameData.mission.wave.OneWaveVO;
   import mogames.gameData.mission.wave.WaveDataVO;
   import mogames.gameData.mission.wave.WaveEnemyVO;
   import mogames.gameData.mission.wave.WaveLimitVO;
   import mogames.gameData.role.battle.BossArgVO;
   import mogames.gameData.role.battle.RoleArgVO;
   import mogames.gameData.skill.boss.BossSkillData0;
   import mogames.gameData.skill.boss.BossSkillData1;
   
   public class NormalWave2069
   {
      
      public function NormalWave2069()
      {
         super();
      }
      
      public function get waveDataVO() : WaveDataVO
      {
         var _loc2_:OneWaveVO = null;
         var _loc1_:WaveDataVO = new WaveDataVO(2069);
         _loc1_.limitBR = new WaveLimitVO(0,1.2,1.3);
         _loc1_.zhuBoss = new BossArgVO(243,700000,4000,650,80,80,300,90,new BossSkillData0(150,{"hurt":8000},5),1012,0);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(247,11700,1340,120,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(7,new RoleArgVO(250,11700,1340,120,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(10);
         _loc2_.addEnemy(new WaveEnemyVO(4,new RoleArgVO(247,11700,1340,120,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(4,new RoleArgVO(249,11700,1340,120,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(10,new RoleArgVO(247,11700,1340,120,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(242,140000,3800,450,50,80,150,80,new BossSkillData1(10,{
            "hurt":4000,
            "hurtCount":5
         },2),1003,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(247,11700,1340,120,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(248,11700,1340,120,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(15,new RoleArgVO(247,11700,1340,120,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(241,140000,3800,450,50,80,150,80,new BossSkillData1(10,{"hurt":4000},2),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(9,new RoleArgVO(247,11700,1340,120,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(9,new RoleArgVO(249,11700,1340,120,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(25);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(247,11700,1340,120,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(250,11700,1340,120,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(20);
         _loc2_.addEnemy(new WaveEnemyVO(8,new RoleArgVO(247,11700,1340,120,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(242,140000,3800,450,50,80,150,80,new BossSkillData0(150,{"hurt":4000},2),1008,0));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(15);
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(247,11700,1340,120,50,50,200,120,0)));
         _loc2_.addEnemy(new WaveEnemyVO(5,new RoleArgVO(248,11700,1340,120,50,50,200,120,0)));
         _loc1_.addWave(_loc2_);
         _loc2_ = new OneWaveVO(30);
         _loc2_.addEnemy(new WaveEnemyVO(13,new RoleArgVO(247,11700,1340,120,50,50,200,120,0)));
         _loc2_.addFu(new BossArgVO(242,100000,3600,450,50,80,150,80,new BossSkillData1(10,{
            "hurt":4000,
            "hurtCount":5
         },2),1003,0));
         _loc2_.addFu(new BossArgVO(241,100000,3600,450,50,80,150,80,new BossSkillData1(12,{
            "hurt":4000,
            "hurtCount":5
         },2),1003,0));
         _loc1_.addWave(_loc2_);
         return _loc1_;
      }
   }
}

